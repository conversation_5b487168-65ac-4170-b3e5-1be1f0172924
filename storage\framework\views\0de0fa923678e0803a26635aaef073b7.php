


<?php $__env->startSection('title'); ?>
    <PERSON><PERSON><PERSON>jar <?php echo e(auth()->user()->name); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h3 class="card-title">
                <i class="fas fa-chalkboard-teacher"></i> Jadwal Mengajar <?php echo e(auth()->user()->name); ?>

            </h3>
            <div class="card-tools">
                <div class="btn-group">
                    <button type="button" class="btn btn-secondary btn-sm dropdown-toggle" data-toggle="dropdown">
                        <i class="fas fa-filter"></i> Tahun Ajaran: <?php echo e($tahunAjaranTerpilih ?? '2023/2024'); ?>

                    </button>
                    <div class="dropdown-menu">
                        <?php if(isset($tahunAjaranList)): ?>
                            <?php $__currentLoopData = $tahunAjaranList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ta): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <a class="dropdown-item <?php echo e($ta == ($tahunAjaranTerpilih ?? '') ? 'active' : ''); ?>"
                                   href="<?php echo e(route('jadwal.mengajar', ['tahun_ajaran' => $ta])); ?>">
                                    <?php echo e($ta); ?>

                                </a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </div>
                </div>
                <a href="<?php echo e(route('jadwal.index')); ?>" class="btn btn-info btn-sm ml-2">
                    <i class="fas fa-list"></i> Tampilan List
                </a>
            </div>
        </div>
    </div>
    <div class="card-body">
        <?php if(session('success')): ?>
            <div class="alert alert-success"><?php echo e(session('success')); ?></div>
        <?php endif; ?>

        <?php if(isset($jadwalList) && $jadwalList->isEmpty()): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> Tidak ada jadwal mengajar untuk tahun ajaran ini.
            </div>
        <?php elseif(isset($jadwalList)): ?>
            <?php $__currentLoopData = $jadwalList->unique('kelas_id'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $jadwal): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="mb-5">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="text-primary">
                            <i class="fas fa-school"></i> <?php echo e($jadwal->kelas->nama); ?>

                        </h4>
                        <div class="d-flex align-items-center">
                            <span class="badge badge-success mr-3">
                                <i class="fas fa-user-tie"></i> <?php echo e($jadwal->kelas->wali_kelas ?? 'Belum ada wali kelas'); ?>

                            </span>
                            <span class="badge badge-info">
                                <i class="fas fa-calendar-alt"></i> <?php echo e($jadwal->kelas->tahun_ajaran); ?>

                            </span>
                        </div>
                    </div>

                    <?php
                        // Siapkan data untuk tabel
                        $hari = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat'];
                        $jamSlots = [];
                        $jadwalData = [];

                        // Kumpulkan semua slot waktu dan data jadwal untuk kelas ini
                        $detailJadwalKelas = $jadwalList->where('kelas_id', $jadwal->kelas_id)->flatMap(function($j) {
                            return $j->detailJadwal;
                        });

                        foreach($detailJadwalKelas as $detail) {
                            $waktu = substr($detail->waktu_mulai, 0, 5) . '-' . substr($detail->waktu_selesai, 0, 5);
                            if (!in_array($waktu, $jamSlots)) {
                                $jamSlots[] = $waktu;
                            }
                            $jadwalData[$detail->hari][$waktu] = $detail;
                        }

                        // Urutkan slot waktu
                        sort($jamSlots);
                    ?>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped jadwal-table">
                            <thead class="thead-dark">
                                <tr>
                                    <th class="jam-column">
                                        <i class="fas fa-clock"></i> Jam
                                    </th>
                                    <?php $__currentLoopData = $hari; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $h): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <th class="hari-column text-center">
                                            <?php echo e($h); ?>

                                        </th>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $jamSlots; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $jam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td class="jam-cell font-weight-bold">
                                            <?php echo e($jam); ?>

                                        </td>
                                        <?php $__currentLoopData = $hari; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $h): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <td class="mapel-cell">
                                                <?php if(isset($jadwalData[$h][$jam])): ?>
                                                    <?php $detail = $jadwalData[$h][$jam]; ?>
                                                    <?php if($detail->is_istirahat || $detail->keterangan): ?>
                                                        <div class="jadwal-item istirahat">
                                                            <div class="mapel-name">
                                                                <i class="fas fa-coffee"></i> <?php echo e($detail->keterangan); ?>

                                                            </div>
                                                        </div>
                                                    <?php else: ?>
                                                        <div class="jadwal-item mapel-mengajar">
                                                            <div class="mapel-name">
                                                                <i class="fas fa-book"></i> <?php echo e($detail->mataPelajaran->nama_mapel); ?>

                                                            </div>
                                                            <div class="kelas-info">
                                                                <i class="fas fa-users"></i> <?php echo e($jadwal->kelas->nama); ?>

                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <div class="jadwal-item kosong">
                                                        <span class="text-muted">-</span>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php else: ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> Data jadwal tidak tersedia.
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('css'); ?>
<style>
    .jadwal-table {
        font-size: 0.9rem;
        border: 2px solid #dee2e6;
    }

    .jadwal-table th {
        background: linear-gradient(135deg, #343a40 0%, #495057 100%);
        color: white;
        text-align: center;
        vertical-align: middle;
        border: 1px solid #495057;
        font-weight: 600;
    }

    .jam-column {
        width: 120px;
        min-width: 120px;
    }

    .hari-column {
        width: calc((100% - 120px) / 5);
        min-width: 150px;
    }

    .jam-cell {
        background-color: #f8f9fa;
        text-align: center;
        vertical-align: middle;
        color: #495057;
        border-right: 2px solid #dee2e6;
    }

    .mapel-cell {
        padding: 8px;
        vertical-align: middle;
        height: 80px;
        position: relative;
    }

    .jadwal-item {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        border-radius: 6px;
        padding: 5px;
        transition: all 0.3s ease;
    }

    .jadwal-item.mapel-mengajar {
        background: linear-gradient(135deg, #e8f5e8 0%, #c3e6c3 100%);
        border: 1px solid #28a745;
        color: #155724;
    }

    .jadwal-item.mapel-mengajar:hover {
        background: linear-gradient(135deg, #c3e6c3 0%, #a4d4a4 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
    }

    .jadwal-item.istirahat {
        background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
        border: 1px solid #ff9800;
        color: #e65100;
    }

    .jadwal-item.istirahat:hover {
        background: linear-gradient(135deg, #ffcc80 0%, #ffb74d 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(255, 152, 0, 0.3);
    }

    .jadwal-item.kosong {
        background-color: #f8f9fa;
        border: 1px dashed #dee2e6;
    }

    .mapel-name {
        font-weight: 600;
        font-size: 0.85rem;
        line-height: 1.2;
        margin-bottom: 2px;
    }

    .kelas-info {
        font-size: 0.75rem;
        opacity: 0.8;
        font-style: italic;
    }

    .card-title i {
        color: #28a745;
    }

    .alert {
        border-radius: 8px;
    }

    .badge {
        font-size: 0.8rem;
        padding: 6px 12px;
    }

    .badge-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }

    .badge-info {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    }

    /* Styling untuk dropdown dan button */
    .card-tools .btn-group {
        margin-right: 5px;
    }

    .dropdown-menu {
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border: none;
    }

    .dropdown-item {
        padding: 8px 16px;
        transition: all 0.3s ease;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
        color: #495057;
        transform: translateX(5px);
    }

    .dropdown-item i {
        width: 20px;
        margin-right: 8px;
    }

    .btn-info {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        border: none;
        box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);
    }

    .btn-info:hover {
        background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(23, 162, 184, 0.4);
    }

    @media (max-width: 768px) {
        .jadwal-table {
            font-size: 0.8rem;
        }

        .hari-column {
            min-width: 120px;
        }

        .jam-column {
            width: 100px;
            min-width: 100px;
        }

        .mapel-cell {
            height: 70px;
            padding: 5px;
        }

        .mapel-name {
            font-size: 0.8rem;
        }

        .kelas-info {
            font-size: 0.7rem;
        }
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<script>
$(document).ready(function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();

    // Flash message fade out
    setTimeout(function() {
        $('.alert-success').fadeOut('slow');
    }, 3000);

    // Add hover effect for better UX
    $('.jadwal-item.mapel-mengajar, .jadwal-item.istirahat').hover(
        function() {
            $(this).css('cursor', 'pointer');
        }
    );

    // Enhanced button hover effects
    $('.btn').hover(
        function() {
            $(this).addClass('shadow-lg');
        },
        function() {
            $(this).removeClass('shadow-lg');
        }
    );

    // Add click effect for jadwal items
    $('.jadwal-item.mapel-mengajar').click(function() {
        var mapel = $(this).find('.mapel-name').text().trim();
        var kelas = $(this).find('.kelas-info').text().trim();

        // Simple info display
        $(this).effect("highlight", {color: "#28a745"}, 1000);

        // You can add more functionality here like showing detail modal
        console.log('Clicked: ' + mapel + ' - ' + kelas);
    });

    // Smooth scroll animation for long tables
    $('.table-responsive').on('scroll', function() {
        var scrolled = $(this).scrollLeft();
        if (scrolled > 0) {
            $(this).addClass('scrolled');
        } else {
            $(this).removeClass('scrolled');
        }
    });

    // Add loading animation for dropdown changes
    $('.dropdown-item[href*="mengajar"]').click(function(e) {
        var $this = $(this);
        var originalText = $this.html();

        // Show loading state
        $this.html('<i class="fas fa-spinner fa-spin"></i> Memuat...');
        $this.addClass('disabled');

        // Let the navigation proceed normally
        // The loading state will be cleared when the page reloads
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\simaspeloporh2_Jadwal Ok2\resources\views/jadwal/mengajar.blade.php ENDPATH**/ ?>