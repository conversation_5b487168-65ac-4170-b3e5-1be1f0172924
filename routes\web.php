<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\GTKController;
use App\Http\Controllers\PesertaDidikController;
use App\Http\Controllers\ADMController;
use App\Http\Controllers\RombelController;
use App\Http\Controllers\JadwalController;
use App\Http\Controllers\AbsensiController;
use App\Http\Controllers\SaranaController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\SPPController;
use App\Http\Controllers\WebsiteController;
use App\Http\Controllers\PengaturanController;
use App\Http\Controllers\BKController;
use App\Http\Controllers\UnitController;
use App\Http\Controllers\JenjangController;
use App\Http\Controllers\AdmGuruController;
use App\Http\Controllers\AdmWakaController;
use App\Http\Controllers\Website\ArtikelController;
use App\Http\Controllers\Admin\SlideController;
use App\Http\Controllers\Website\EventController;
use App\Http\Controllers\Website\HalamanController;
use App\Http\Controllers\Website\JenjangController as WebsiteJenjangController;
use App\Http\Controllers\Website\PrestasiController;
use App\Http\Controllers\Website\FacilityController;
use App\Http\Controllers\Website\ExtrakurikulerController;
use App\Http\Controllers\JurnalKegiatanController;
use App\Http\Controllers\AdmKepsekController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\GuruController;
use App\Http\Controllers\TendikController;
use Illuminate\Support\Facades\Artisan;

//simpling
Route::get('/storage-link', function () {
    Artisan::call('storage:link');
    return 'Storage linked successfully.';
});

Route::get('/', [WebsiteController::class, 'index'])->name('website.home');

// Route untuk prestasi publik (tanpa auth)
Route::get('/prestasi', [WebsiteController::class, 'allPrestasi'])
    ->name('website.prestasi.all');
Route::get('/prestasi/{jenjang}', [WebsiteController::class, 'prestasiByJenjang'])
    ->name('website.prestasi.jenjang')
    ->where('jenjang', 'paud|sd|smp|sma');

Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Unit Routes
    Route::get('/pengaturan/unit', [UnitController::class, 'index'])->name('pengaturan.unit');
    Route::post('/pengaturan/unit', [UnitController::class, 'store'])->name('unit.store');
    Route::put('/pengaturan/unit/{unit}', [UnitController::class, 'update'])->name('unit.update');
    Route::delete('/pengaturan/unit/{unit}', [UnitController::class, 'destroy'])->name('unit.destroy');

    // GTK Routes
    //Route::prefix('gtk')->middleware('permission:view-gtk')->group(function () {
        //Route::get('/', [GTKController::class, 'index'])->name('gtk.index');
        
        // Hanya yang punya permission manage-gtk yang bisa CRUD
       // Route::middleware('permission:manage-gtk')->group(function () {
            //Route::post('/', [GTKController::class, 'store']);
           // Route::put('/{id}', [GTKController::class, 'update']);
           // Route::delete('/{id}', [GTKController::class, 'destroy']);
       // });
   // });

    // Peserta Didik Routes
    Route::prefix('peserta-didik')->group(function () {
        Route::get('/aktif', [PesertaDidikController::class, 'aktif'])->name('peserta-didik.aktif');
        Route::get('/alumni', [PesertaDidikController::class, 'alumni'])->name('peserta-didik.alumni');
        Route::get('/mutasi', [PesertaDidikController::class, 'mutasi'])->name('peserta-didik.mutasi');
        Route::get('/mutasi-keluar', [PesertaDidikController::class, 'mutasiKeluar'])->name('peserta-didik.mutasi-keluar');
        // Tambahkan route untuk edit dan destroy mutasi
        Route::get('/mutasi/{id}/edit', [PesertaDidikController::class, 'editMutasi'])->name('peserta-didik.mutasi.edit');
        Route::delete('/mutasi/{id}', [PesertaDidikController::class, 'destroyMutasi'])->name('peserta-didik.mutasi.destroy');
        
        // Import Routes
        //Route::post('/import', [PesertaDidikController::class, 'import'])->name('peserta-didik.import');
        //Route::get('/export-template', [PesertaDidikController::class, 'exportTemplate'])->name('peserta-didik.export-template');
        //Route::get('/export', [PesertaDidikController::class, 'export'])->name('peserta-didik.export');
        
        // CRUD Routes
        Route::post('/store', [PesertaDidikController::class, 'store'])->name('peserta-didik.store');
        Route::put('/{id}', [PesertaDidikController::class, 'update'])->name('peserta-didik.update');
        Route::delete('/{id}', [PesertaDidikController::class, 'destroy'])->name('peserta-didik.destroy');
    });

    // ADM Routes
    Route::prefix('adm')->group(function () {
        Route::prefix('guru')
            ->name('adm.guru.')
            ->middleware(['auth', 'check.adm.guru.access', 'filter.unit'])
            ->group(function () {
                Route::get('/', [AdmGuruController::class, 'index'])->name('index');
                Route::post('/', [AdmGuruController::class, 'store'])->name('store');
                Route::get('/view-page/{filename}', function($filename) {
                    return view('adm.guru.view-pdf', compact('filename'));
                })->name('view-page');
                Route::get('/view/{filename}', [AdmGuruController::class, 'viewFile'])->name('view');
                Route::post('/{adm}/approve', [AdmGuruController::class, 'approve'])->name('approve');
                Route::post('/{adm}/reject', [AdmGuruController::class, 'reject'])->name('reject');
            });
    });

    // ADM Waka Routes
    Route::prefix('adm/waka')
        ->name('adm.waka.')
        ->middleware(['auth', 'check.adm.waka.access', 'filter.unit'])
        ->group(function () {
            Route::get('/', [App\Http\Controllers\AdmWakaController::class, 'index'])->name('index');
            Route::post('/', [App\Http\Controllers\AdmWakaController::class, 'store'])->name('store');
            
            // Tambahkan route untuk halaman view PDF
            Route::get('/view-page/{filename}', [App\Http\Controllers\AdmWakaController::class, 'viewPage'])->name('view-page');
            
            // Ubah nama route untuk view file
            Route::get('/view-file/{filename}', [App\Http\Controllers\AdmWakaController::class, 'viewFile'])->name('view-file');
            
            // Route lainnya tetap sama
            Route::get('/download/{filename}', [App\Http\Controllers\AdmWakaController::class, 'download'])->name('download');
            Route::post('/{adm}/approve', [App\Http\Controllers\AdmWakaController::class, 'approve'])->name('approve');
            Route::post('/{adm}/reject', [App\Http\Controllers\AdmWakaController::class, 'reject'])->name('reject');
        });

    // E-Learning Route
    // Route::get('/elearning', [ElearningController::class, 'index'])->name('elearning');

    // Rombongan Belajar Routes
    Route::prefix('rombel')->group(function () {
        // Reguler routes
        Route::prefix('reguler')->group(function () {
            Route::get('/', [RombelController::class, 'reguler'])->name('rombel.reguler');
            Route::get('/daftar', [RombelController::class, 'daftarKelas'])
                ->name('rombel.reguler.daftar')
                ->middleware('permission:view-daftar-kelas');
            
            // Penempatan siswa routes
            Route::get('/penempatan', [RombelController::class, 'penempatan'])->name('rombel.reguler.penempatan');
            Route::post('/penempatan/individu', [RombelController::class, 'penempatanIndividu'])->name('rombel.reguler.penempatan.individu');
            Route::post('/penempatan/kelas', [RombelController::class, 'penempatanKelas'])->name('rombel.reguler.penempatan.kelas');
            
            // Kenaikan kelas routes
            Route::get('/kenaikan', [RombelController::class, 'kenaikan'])->name('rombel.reguler.kenaikan');
            Route::post('/kenaikan/individu', [RombelController::class, 'kenaikanIndividu'])->name('rombel.reguler.kenaikan.individu');
            Route::post('/kenaikan/kelas', [RombelController::class, 'kenaikanKelas'])->name('rombel.reguler.kenaikan.kelas');
            
            // Kelulusan routes
            Route::get('/kelulusan', [RombelController::class, 'kelulusan'])->name('rombel.reguler.kelulusan');
            Route::post('/kelulusan/individu', [RombelController::class, 'kelulusanIndividu'])->name('rombel.reguler.kelulusan.individu');
            Route::post('/kelulusan/kelas', [RombelController::class, 'kelulusanKelas'])->name('rombel.reguler.kelulusan.kelas');
        });
        
        Route::get('/eksul', [RombelController::class, 'eksul'])->name('rombel.eksul');
    });

    // Jadwal Routes
    Route::get('/jadwal/export/{tahunAjaran}', [JadwalController::class, 'export'])
        ->name('jadwal.export');
    
    Route::get('/jadwal/guru', [JadwalController::class, 'jadwalGuru'])
        ->name('jadwal.guru');
        
    Route::get('/jadwal/mengajar', [JadwalController::class, 'jadwalMengajar'])
        ->name('jadwal.mengajar')
        ->middleware('auth');

    Route::get('/jadwal/jadlaykot', [JadwalController::class, 'jadlaykot'])
        ->name('jadwal.jadlaykot')
        ->middleware('auth');

    Route::get('/jadwal/perguru', [JadwalController::class, 'perguru'])
        ->name('jadwal.perguru')
        ->middleware('auth');

    Route::resource('jadwal', JadwalController::class);

    // API untuk mendapatkan jadwal guru
    Route::get('/api/jadwal/guru', [JadwalController::class, 'getJadwalByGuru'])
        ->name('api.jadwal.guru');


    //Route::get('/jadwal-guru', [JadwalController::class, 'jadwalGuru'])->name('jadwal.guru');
    
    // Absensi Routes
    Route::prefix('absensi')->middleware('auth')->group(function () {
        Route::get('/harian', [AbsensiController::class, 'harian'])->name('absensi.harian');
        Route::get('/rekap', [AbsensiController::class, 'rekap'])->name('absensi.rekap');
        Route::get('/get-students', [AbsensiController::class, 'getStudentsByClass'])->name('absensi.get-students');
        Route::post('/save', [AbsensiController::class, 'saveAttendance'])->name('absensi.save');
        Route::get('/get-rekap', [AbsensiController::class, 'getRekapAbsensi'])->name('absensi.get-rekap');
        Route::get('/export-excel', [AbsensiController::class, 'exportExcel'])->name('absensi.export-excel');
    });
    // Route export excel absensi
    //Route::get('/absensi/export-excel', [AbsensiController::class, 'exportExcel'])->name('absensi.export-excel');
    Route::get('/absensi/export', [App\Http\Controllers\AbsensiController::class, 'exportExcel'])->name('absensi.export');
    
    // Sarpras Routes
    Route::prefix('sarpras')->group(function () {
        Route::get('/kerusakan', [SaranaController::class, 'kerusakan'])->name('sarpras.kerusakan');
        Route::get('/data', [SaranaController::class, 'data'])->name('sarpras.data');
    });

    // Users Route
    Route::get('/users', [UserController::class, 'index'])->name('users');

    // SPP Route
    Route::get('/spp', [SPPController::class, 'index'])->name('spp');

    // Website Routes
    Route::prefix('website')->middleware('permission:view-website')->group(function () {
        Route::get('/artikel', [WebsiteController::class, 'artikel'])->name('website.artikel');
        
        // Route prestasi yang dapat diakses semua yang punya permission view-website
        Route::get('/prestasi', [WebsiteController::class, 'prestasi'])->name('website.prestasi');
        
        // Route untuk CRUD prestasi yang memerlukan permission khusus
        Route::middleware('permission:manage-website-prestasi')->group(function () {
            Route::post('/prestasi', [WebsiteController::class, 'storePrestasi']);
            Route::put('/prestasi/{id}', [WebsiteController::class, 'updatePrestasi']);
            Route::delete('/prestasi/{id}', [WebsiteController::class, 'destroyPrestasi']);
        });
        
        Route::get('/event', [WebsiteController::class, 'event'])->name('website.event');
        Route::get('/fasilitas', [App\Http\Controllers\Website\FacilityController::class, 'show'])->name('website.fasilitas');
        //Route::get('/ekstrakurikuler', [WebsiteController::class, 'ekstrakurikuler'])->name('website.ekstrakurikuler');
        Route::get('/slide', [SlideController::class, 'index'])->name('website.slide');
        Route::get('/slide/create', [SlideController::class, 'create'])->name('website.slide.create');
        Route::post('/slide', [SlideController::class, 'store'])->name('website.slide.store');
        Route::get('/slide/{slide}/edit', [SlideController::class, 'edit'])->name('website.slide.edit');
        Route::put('/slide/{slide}', [SlideController::class, 'update'])->name('website.slide.update');
        Route::delete('/slide/{slide}', [SlideController::class, 'destroy'])->name('website.slide.destroy');
        Route::prefix('halaman')->group(function () {
            Route::get('/profil', [WebsiteController::class, 'profil'])->name('website.halaman.profil');
            Route::get('/visi-misi', [WebsiteController::class, 'visiMisi'])->name('website.halaman.visi-misi');
            Route::get('/sejarah', [WebsiteController::class, 'sejarah'])->name('website.halaman.sejarah');
        });
    });

    // BK Routes
    /*
    Route::prefix('bk')->group(function () {
        Route::get('/pelanggaran', [BKController::class, 'pelanggaran'])->name('bk.pelanggaran');
        Route::get('/sanksi', [BKController::class, 'sanksi'])->name('bk.sanksi');
        Route::get('/data-pelanggaran', [BKController::class, 'dataPelanggaran'])->name('bk.data-pelanggaran');
        return redirect()->route('admin.under-development');
    }); */

// Asumsikan Anda memiliki App\Http\Controllers\BKController jika masih ada route lain yang menggunakannya.
// Jika tidak, Anda tidak perlu meng-importnya untuk kode di bawah ini.

Route::prefix('bk')->group(function () {
    Route::get('/pelanggaran', function () {
        return redirect()->route('admin.under-development');
    })->name('bk.pelanggaran');

    Route::get('/sanksi', function () {
        return redirect()->route('admin.under-development');
    })->name('bk.sanksi');

    Route::get('/data-pelanggaran', function () {
        return redirect()->route('admin.under-development');
    })->name('bk.data-pelanggaran');
});

// Pastikan Anda juga memiliki definisi route untuk 'admin.under-development', contohnya:
// Route::get('/under-development', [AdminController::class, 'underDevelopment'])->name('admin.under-development');

    // Pengaturan Routes
    Route::prefix('pengaturan')->group(function () {
        // Mata Pelajaran Routes
        Route::get('/mapel', [PengaturanController::class, 'mapel'])->name('pengaturan.mapel');
        Route::post('/mapel/store', [PengaturanController::class, 'mapelStore'])->name('mapel.store');
        Route::put('/mapel/{mapel}', [PengaturanController::class, 'mapelUpdate'])->name('mapel.update');
        Route::delete('/mapel/{mapel}', [PengaturanController::class, 'mapelDestroy'])->name('mapel.destroy');

        // Kelas Routes
        Route::get('/kelas', [PengaturanController::class, 'kelas'])->name('pengaturan.kelas');
        Route::post('/kelas/store', [PengaturanController::class, 'kelasStore'])->name('pengaturan.kelas.store');
        Route::put('/kelas/{kelas}', [PengaturanController::class, 'kelasUpdate'])->name('pengaturan.kelas.update');
        Route::delete('/kelas/{kelas}', [PengaturanController::class, 'kelasDestroy'])->name('pengaturan.kelas.destroy');
    });

    // Gedung Routes
    Route::prefix('pengaturan')->group(function () {
        Route::get('/gedung', [PengaturanController::class, 'gedung'])->name('pengaturan.gedung');
        Route::post('/gedung', [PengaturanController::class, 'gedungStore'])->name('gedung.store');
        Route::put('/gedung/{gedung}', [PengaturanController::class, 'gedungUpdate'])->name('gedung.update');
        Route::delete('/gedung/{gedung}', [PengaturanController::class, 'gedungDestroy'])->name('gedung.destroy');
    });

    // Users Management Routes
    Route::prefix('users')->middleware(['auth'])->group(function () {
        Route::get('/', [UserController::class, 'index'])->name('users.index');
        Route::get('/create', [UserController::class, 'create'])->name('users.create');
        Route::post('/', [UserController::class, 'store'])->name('users.store');
        Route::get('/{user}/edit', [UserController::class, 'edit'])->name('users.edit');
        Route::put('/{user}', [UserController::class, 'update'])->name('users.update');
        Route::delete('/{user}', [UserController::class, 'destroy'])->name('users.destroy');
    });

    // Jenjang Routes
    Route::prefix('pengaturan/jenjang')->group(function () {
        Route::get('/', [JenjangController::class, 'index'])->name('jenjang.index');
        Route::post('/', [JenjangController::class, 'store'])->name('jenjang.store');
        Route::put('/{id}', [JenjangController::class, 'update'])->name('jenjang.update');
        Route::delete('/{id}', [JenjangController::class, 'destroy'])->name('jenjang.destroy');
    });

    // Routes untuk ADM Guru - tanpa middleware dan pengecekan
    Route::prefix('adm/guru')
        ->name('adm.guru.')
        ->group(function () {
            Route::get('/', [AdmGuruController::class, 'index'])->name('index');
            Route::post('/', [AdmGuruController::class, 'store'])->name('store');
            Route::get('/view-page/{filename}', function($filename) {
                return view('adm.guru.view-pdf', compact('filename'));
            })->name('view-page');
            Route::get('/view/{filename}', [AdmGuruController::class, 'viewFile'])->name('view');
            Route::post('/{adm}/approve', [AdmGuruController::class, 'approve'])->name('approve');
            Route::post('/{adm}/reject', [AdmGuruController::class, 'reject'])->name('reject');
        });

    // Route untuk halaman iframe
    Route::get('/adm-guru/view-page/{filename}', function($filename) {
        return view('adm.guru.view-pdf', compact('filename'));
    })->name('adm.guru.view-page');

    // Route untuk menampilkan file PDF
    Route::get('/adm-guru/view/{filename}', [AdmGuruController::class, 'viewFile'])
        ->name('adm.guru.view');

    // ADM Kepsek Routes
    Route::prefix('adm/kepsek')
        ->name('adm.kepsek.')
        ->middleware(['auth', 'check.adm.kepsek.access', 'filter.unit'])
        ->group(function () {
            Route::get('/', [App\Http\Controllers\AdmKepsekController::class, 'index'])->name('index');
            Route::post('/', [App\Http\Controllers\AdmKepsekController::class, 'store'])->name('store');
            Route::get('/view-page/{filename}', function($filename) {
                return view('adm.kepsek.view-pdf', compact('filename'));
            })->name('view-page');
            Route::get('/view/{filename}', [App\Http\Controllers\AdmKepsekController::class, 'viewFile'])->name('view');
            Route::post('/{adm}/approve', [App\Http\Controllers\AdmKepsekController::class, 'approve'])->name('approve');
            Route::post('/{adm}/reject', [App\Http\Controllers\AdmKepsekController::class, 'reject'])->name('reject');
        });

    // KTSP Routes
    Route::prefix('adm/ktsp')
        ->name('adm.ktsp.')
        ->middleware(['auth', 'check.adm.ktsp.access'])
        ->group(function () {
            Route::get('/', [App\Http\Controllers\AdmKtspController::class, 'index'])->name('index');
            Route::post('/', [App\Http\Controllers\AdmKtspController::class, 'store'])->name('store');
            Route::get('/view-page/{filename}', [App\Http\Controllers\AdmKtspController::class, 'viewPage'])->name('view-page');
            Route::get('/view/{filename}', [App\Http\Controllers\AdmKtspController::class, 'viewFile'])->name('view');
            Route::get('/download/{filename}', [App\Http\Controllers\AdmKtspController::class, 'download'])->name('download');
            Route::get('/delete/{adm}', [App\Http\Controllers\AdmKtspController::class, 'delete'])->name('delete');
        });
});

require __DIR__.'/auth.php';

// Tambahkan route ini untuk testing
Route::get('/test-db', function() {
    try {
        $database_name = DB::connection()->getDatabaseName();
        return "Terhubung ke database: " . $database_name;
    } catch(\Exception $e) {
        return "Tidak terhubung ke database: " . $e->getMessage();
    }
});

// Website Routes
Route::get('/', [WebsiteController::class, 'index'])->name('website.home');
Route::get('/artikel', [WebsiteController::class, 'artikel'])->name('website.artikel');
Route::get('/artikel/{slug}', [WebsiteController::class, 'showArtikel'])->name('website.artikel.show');
Route::get('/profil-sekolah', [WebsiteController::class, 'profil'])->name('website.halaman.profil');
Route::get('/visi-misi', [WebsiteController::class, 'visiMisi'])->name('website.halaman.visi-misi');
Route::get('/sejarah', [WebsiteController::class, 'sejarah'])->name('website.halaman.sejarah');

Route::middleware(['auth', 'permission:view-website'])->group(function () {
    Route::prefix('website')->group(function () {
        Route::get('/slide', [SlideController::class, 'index'])->name('website.slide');
        Route::get('/slide/create', [SlideController::class, 'create'])->name('website.slide.create');
        Route::post('/slide', [SlideController::class, 'store'])->name('website.slide.store');
        Route::get('/slide/{slide}/edit', [SlideController::class, 'edit'])->name('website.slide.edit');
        Route::put('/slide/{slide}', [SlideController::class, 'update'])->name('website.slide.update');
        Route::delete('/slide/{slide}', [SlideController::class, 'destroy'])->name('website.slide.destroy');
    });
});

Route::middleware(['auth'])->prefix('website')->name('website.')->group(function () {
    Route::resource('artikel', ArtikelController::class)->except(['show']);
});

// Route untuk artikel publik
Route::get('/artikel', [WebsiteController::class, 'artikel'])->name('website.artikel');
Route::get('/artikel/{slug}', [WebsiteController::class, 'showArtikel'])->name('website.artikel.show');

// Route untuk manajemen artikel (admin)
Route::middleware(['auth', 'permission:manage-website|manage-website-article'])
    ->prefix('admin/website')
    ->name('admin.website.')
    ->group(function () {
        Route::resource('artikel', App\Http\Controllers\Website\ArtikelController::class);
    });

// Route untuk halaman event publik
Route::get('/event', [WebsiteController::class, 'event'])->name('website.event');

// Route untuk manajemen event (admin)
Route::middleware(['auth', 'permission:manage-website|manage-website-event'])
    ->prefix('admin/website')
    ->name('admin.website.')
    ->group(function () {
        // Pastikan tidak ada route lain yang menggunakan nama 'event'
        Route::resource('event', \App\Http\Controllers\Website\EventController::class);
    });

// Hapus atau komentar route lain yang mungkin konflik
// Route::get('/event', [WebsiteController::class, 'event'])->name('website.event');

// Route untuk menampilkan semua event
Route::get('/blog/event', [WebsiteController::class, 'allEvents'])->name('website.event.all');

// Routes untuk halaman unit
Route::prefix('blog/{jenjang}')->group(function () {
    Route::get('/', [WebsiteJenjangController::class, 'showProfil'])
        ->name('website.jenjang.profil');
    Route::get('/visi-misi', [WebsiteJenjangController::class, 'showVisiMisi'])
        ->name('website.jenjang.visi-misi');
    Route::get('/sejarah', [WebsiteJenjangController::class, 'showSejarah'])
        ->name('website.jenjang.sejarah');
    Route::get('/artikel', [WebsiteJenjangController::class, 'showArtikel'])
        ->name('website.jenjang.artikel');
    Route::get('/event', [WebsiteJenjangController::class, 'showEvent'])
        ->name('website.jenjang.event');
    Route::get('/prestasi', [WebsiteJenjangController::class, 'showPrestasi'])
        ->name('website.jenjang.prestasi');
    Route::get('/ekstrakurikuler', [WebsiteJenjangController::class, 'showEkstrakurikuler'])
        ->name('website.jenjang.ekstrakurikuler');
})->where('jenjang', 'paud|sd|smp|sma');

// Route untuk manajemen prestasi (admin)
Route::middleware(['auth', 'permission:manage-website|manage-website-prestasi'])
    ->prefix('admin/website')
    ->name('admin.website.')
    ->group(function () {
        Route::resource('prestasi', \App\Http\Controllers\Website\PrestasiController::class);
    });


// Route yang membutuhkan auth
Route::middleware(['auth'])->group(function () {
    Route::prefix('admin/website')->name('admin.website.')->group(function () {
        Route::resource('halaman', HalamanController::class);
    });
});

// Admin routes fasilitas
Route::middleware(['auth', 'role:Administrator'])->prefix('admin')->name('admin.')->group(function () {
    Route::prefix('website')->name('website.')->group(function () {
        Route::resource('facility', FacilityController::class)->except(['show']);
    });
});

// Website routes fasilitas
Route::get('/fasilitas', [FacilityController::class, 'show'])->name('website.fasilitas');

Route::middleware(['auth', 'permission:manage-website'])->group(function () {
    Route::prefix('admin/website')->name('admin.website.')->group(function () {
        // Ekstrakurikuler routes
        Route::resource('ekstrakurikuler', EkstrakurikulerController::class);
    });
});

// Route publik untuk ekstrakurikuler
Route::get('/ekstrakurikuler', [WebsiteController::class, 'ekstrakurikuler'])
    ->name('website.ekstrakurikuler');

// Route untuk ekstrakurikuler per jenjang
Route::get('/ekstrakurikuler/{jenjang}', [WebsiteController::class, 'ekstrakurikulerByJenjang'])
    ->name('website.ekstrakurikuler.jenjang')
    ->where('jenjang', 'paud|sd|smp|sma');

// Route untuk manajemen ekstrakurikuler (admin)
Route::middleware(['auth', 'permission:manage-website'])
    ->prefix('admin/website')
    ->name('admin.website.')
    ->group(function () {
        Route::resource('ekstrakurikuler', ExtrakurikulerController::class);
    });
    // route jurnal
    // Routes untuk jurnal - pindahkan ke dalam grup middleware filter.unit /'auth', 'check.jurnal', 'filter.unit'
Route::middleware(['auth', 'check.jurnal', 'filter.unit'])->prefix('jurnal')->name('jurnal.')->group(function () {
    Route::get('list', [JurnalKegiatanController::class, 'index'])->name('index');
    Route::post('submit', [JurnalKegiatanController::class, 'submit'])->name('submit');
    
    // Tambahkan route untuk show/detail jurnal
    Route::get('{jurnal}/show', [JurnalKegiatanController::class, 'show'])->name('show');
    
    // Route untuk jurnal yang disetujui
    Route::get('approved', [JurnalKegiatanController::class, 'approved'])->name('approved');
    
    // Route untuk rekap jurnal
    Route::get('recap', [JurnalKegiatanController::class, 'recap'])->name('recap');
    
    // Tambahkan route untuk submit individual
    Route::post('{jurnal}/submit', [JurnalKegiatanController::class, 'submitIndividual'])->name('submitIndividual');
    
    // Routes untuk yang memiliki permission manage-jurnal
    Route::middleware(['permission:manage-jurnal'])->group(function () {
        Route::get('create', [JurnalKegiatanController::class, 'create'])->name('create');
        Route::post('/', [JurnalKegiatanController::class, 'store'])->name('store');
        Route::get('{jurnal}/edit', [JurnalKegiatanController::class, 'edit'])->name('edit');
        Route::put('{jurnal}', [JurnalKegiatanController::class, 'update'])->name('update');
        Route::delete('{jurnal}', [JurnalKegiatanController::class, 'destroy'])->name('destroy');
    });

    // Route khusus untuk approval
    Route::middleware(['permission:approve-jurnal'])->group(function () {
        Route::get('approval', [JurnalKegiatanController::class, 'approval'])->name('approval');
        Route::post('approve/{userId}', [JurnalKegiatanController::class, 'approve'])->name('approve');
        Route::post('approve/individual/{id}', [JurnalKegiatanController::class, 'approveIndividual'])->name('approve.individual');
        Route::post('reject/individual/{id}', [JurnalKegiatanController::class, 'rejectIndividual'])->name('reject.individual');
    });
});

// Route untuk import dan export peserta didik
Route::post('/peserta-didik/import', [App\Http\Controllers\PesertaDidikController::class, 'import'])->name('peserta-didik.import');
Route::get('/peserta-didik/export', [App\Http\Controllers\PesertaDidikController::class, 'export'])->name('peserta-didik.export');
Route::get('/peserta-didik/export-template', [App\Http\Controllers\PesertaDidikController::class, 'exportTemplate'])->name('peserta-didik.export-template');

// Rute untuk pindah kelas
Route::get('/rombel/reguler/pindah-kelas', [RombelController::class, 'pindahKelas'])->name('rombel.reguler.pindah-kelas');
Route::post('/rombel/reguler/pindah-kelas/individu', [RombelController::class, 'pindahKelasIndividu'])->name('rombel.reguler.pindah-kelas.individu');
Route::post('/rombel/reguler/pindah-kelas/massal', [RombelController::class, 'pindahKelasMassal'])->name('rombel.reguler.pindah-kelas.massal');

// Tahun Ajaran routes
Route::middleware(['auth'])->group(function () {
    // Route untuk melihat daftar tahun ajaran (hanya memerlukan permission view-tahunajaran)
    Route::get('/tahunajaran', [App\Http\Controllers\TahunAjaranController::class, 'index'])
        ->middleware('permission:view-tahunajaran')
        ->name('pengaturan.tahunajaran');
    
    // Route untuk mengelola tahun ajaran (memerlukan permission manage-tahunajaran)
    Route::middleware('permission:manage-tahunajaran')->group(function () {
        Route::post('/tahunajaran', [App\Http\Controllers\TahunAjaranController::class, 'store'])
            ->name('tahunajaran.store');
        Route::put('/tahunajaran/{tahunAjaran}', [App\Http\Controllers\TahunAjaranController::class, 'update'])
            ->name('tahunajaran.update');
        Route::delete('/tahunajaran/{tahunAjaran}', [App\Http\Controllers\TahunAjaranController::class, 'destroy'])
            ->name('tahunajaran.destroy');
        Route::patch('/tahunajaran/{tahunAjaran}/set-active', [App\Http\Controllers\TahunAjaranController::class, 'setActive'])
            ->name('tahunajaran.setActive');
    });
});

// API route untuk mendapatkan siswa berdasarkan kelas
Route::get('/api/kelas/{kelasId}/siswa', [RombelController::class, 'getSiswaByKelas'])->name('api.kelas.siswa');

// Tambahkan jika belum ada
Route::resource('sarana', App\Http\Controllers\SaranaController::class);

// Grup route yang memerlukan filter unit
Route::middleware(['auth', 'filter.unit'])->group(function () {
    Route::get('peserta-didik/aktif', [PesertaDidikController::class, 'aktif'])->name('peserta-didik.aktif');
    Route::get('peserta-didik/alumni', [PesertaDidikController::class, 'alumni'])->name('peserta-didik.alumni');
    
    // Tambahkan route RombelController
    Route::prefix('rombel')->name('rombel.')->group(function () {
        Route::get('reguler', [RombelController::class, 'reguler'])->name('reguler');
        Route::get('reguler/daftar', [RombelController::class, 'daftarKelas'])->name('reguler.daftar');
        Route::get('reguler/penempatan', [RombelController::class, 'penempatan'])->name('reguler.penempatan');
        Route::post('reguler/penempatan/individu', [RombelController::class, 'penempatanIndividu'])->name('reguler.penempatan.individu');
        Route::post('reguler/penempatan/kelas', [RombelController::class, 'penempatanKelas'])->name('reguler.penempatan.kelas');
        Route::get('reguler/kenaikan', [RombelController::class, 'kenaikan'])->name('reguler.kenaikan');
        Route::post('reguler/kenaikan/individu', [RombelController::class, 'kenaikanIndividu'])->name('reguler.kenaikan.individu');
        Route::post('reguler/kenaikan/kelas', [RombelController::class, 'kenaikanKelas'])->name('reguler.kenaikan.kelas');
        Route::get('reguler/kelulusan', [RombelController::class, 'kelulusan'])->name('reguler.kelulusan');
        Route::post('reguler/kelulusan/individu', [RombelController::class, 'kelulusanIndividu'])->name('reguler.kelulusan.individu');
        Route::post('reguler/kelulusan/kelas', [RombelController::class, 'kelulusanKelas'])->name('reguler.kelulusan.kelas');
        Route::get('reguler/pindah-kelas', [RombelController::class, 'pindahKelas'])->name('reguler.pindah-kelas');
        Route::post('reguler/pindah-kelas/individu', [RombelController::class, 'pindahKelasIndividu'])->name('reguler.pindah-kelas.individu');
        Route::post('reguler/pindah-kelas/massal', [RombelController::class, 'pindahKelasMassal'])->name('reguler.pindah-kelas.massal');
        Route::get('eksul', [RombelController::class, 'eksul'])->name('eksul');
        
    });
    
    // Route lain yang memerlukan filter unit
    Route::get('peserta-didik/mutasi-keluar', [PesertaDidikController::class, 'mutasiKeluar'])->name('peserta-didik.mutasi-keluar');
    // Rute lain yang memerlukan filter unit
});

// Routes untuk manajemen user dan role
Route::prefix('admin')->name('admin.')->middleware(['auth', 'permission:manage-users'])->group(function () {
    // User Management
    Route::get('/users', [App\Http\Controllers\UserManagementController::class, 'index'])->name('users.index');
    Route::get('/users/{id}/edit', [App\Http\Controllers\UserManagementController::class, 'edit'])->name('users.edit');
    Route::put('/users/{id}', [App\Http\Controllers\UserManagementController::class, 'update'])->name('users.update');
    
    // Role Management
    Route::get('/roles', [App\Http\Controllers\RoleManagementController::class, 'index'])->name('roles.index');
    Route::get('/roles/create', [App\Http\Controllers\RoleManagementController::class, 'create'])->name('roles.create');
    Route::post('/roles', [App\Http\Controllers\RoleManagementController::class, 'store'])->name('roles.store');
    Route::get('/roles/{id}/edit', [App\Http\Controllers\RoleManagementController::class, 'edit'])->name('roles.edit');
    Route::put('/roles/{id}', [App\Http\Controllers\RoleManagementController::class, 'update'])->name('roles.update');
    
    // Permission Management (opsional)
    Route::get('/permissions', [App\Http\Controllers\PermissionController::class, 'index'])->name('permissions.index');
});

// Routes untuk manajemen permission
Route::prefix('admin')->name('admin.')->middleware(['auth', 'permission:manage-permissions'])->group(function () {
    Route::resource('permissions', App\Http\Controllers\PermissionController::class);
});
// Route untuk mutasi
Route::post('/rombel/mutasi-keluar', [RombelController::class, 'mutasiKeluar'])->name('rombel.mutasi-keluar');

// Route untuk update mutasi
Route::put('/peserta-didik/mutasi/{id}', [PesertaDidikController::class, 'updateMutasi'])->name('peserta-didik.update-mutasi');

// User Management Routes - tanpa middleware permission dulu
Route::middleware(['auth'])->prefix('admin')->group(function () {
    // Users
    Route::get('/users', [App\Http\Controllers\UserController::class, 'index'])->name('users.index');
    Route::get('/users/create', [App\Http\Controllers\UserController::class, 'create'])->name('users.create');
    Route::post('/users', [App\Http\Controllers\UserController::class, 'store'])->name('users.store');
    //Route::get('/users/{user}/edit', [App\Http\Controllers\UserController::class, 'edit'])->name('users.edit');
    //Route::put('/users/{user}', [App\Http\Controllers\UserController::class, 'update'])->name('users.update');
    Route::delete('/users/{user}', [App\Http\Controllers\UserController::class, 'destroy'])->name('users.destroy');
});
// untuk mengambil jadwal
Route::get('/jurnal/get-jadwal', [JurnalKegiatanController::class, 'getJadwal'])->name('jurnal.get-jadwal');

// SPP Routes
Route::get('/cek-spp', [App\Http\Controllers\SppController::class, 'check'])->name('spp.check');
Route::post('/cek-spp/hasil', [App\Http\Controllers\SppController::class, 'checkResult'])->name('spp.check.result');

// Admin SPP Routes
Route::prefix('spp')->middleware(['auth'])->middleware('role:Administrator|Tata Usaha')->group(function () {
    Route::get('/', [App\Http\Controllers\SppController::class, 'index'])->name('spp.index');
    Route::post('/upload', [App\Http\Controllers\SppController::class, 'upload'])->name('spp.upload');
});

// Tambahkan route group untuk guru GTK
Route::middleware(['auth', 'filter.unit'])->prefix('gtk')->name('gtk.')->group(function () {
    // Route untuk guru
    Route::get('/guru', [GuruController::class, 'index'])->name('guru.index');
    Route::get('/guru/create', [GuruController::class, 'create'])->name('guru.create');
    Route::post('/guru', [GuruController::class, 'store'])->name('guru.store');
    Route::get('/guru/{id}', [GuruController::class, 'show'])->name('guru.show');
    Route::get('/guru/{id}/edit', [GuruController::class, 'edit'])->name('guru.edit');
    Route::put('/guru/{id}', [GuruController::class, 'update'])->name('guru.update');
    Route::delete('/guru/{id}', [GuruController::class, 'destroy'])->name('guru.destroy');
    Route::get('/guru/unit/{unitId}', [GuruController::class, 'getUnitData'])->name('guru.unit');
    
    // Route untuk tenaga kependidikan
    Route::get('/tendik', [TendikController::class, 'index'])->name('tendik.index');
    Route::get('/tendik/create', [TendikController::class, 'create'])->name('tendik.create');
    Route::post('/tendik', [TendikController::class, 'store'])->name('tendik.store');
    Route::get('/tendik/{id}', [TendikController::class, 'show'])->name('tendik.show');
    Route::get('/tendik/{id}/edit', [TendikController::class, 'edit'])->name('tendik.edit');
    Route::put('/tendik/{id}', [TendikController::class, 'update'])->name('tendik.update');
    Route::delete('/tendik/{id}', [TendikController::class, 'destroy'])->name('tendik.destroy');

    // Route untuk GTK Non-Aktif
    Route::get('/gtk/non-aktif-list', [App\Http\Controllers\GTKController::class, 'nonAktif'])->name('gtk.non-aktif');
    Route::post('/gtk/aktivasi/{id}', [App\Http\Controllers\GTKController::class, 'aktivasi'])->name('gtk.aktivasi');
    
    // Routes untuk pengembangan diri
    Route::post('/guru/{id}/pengembangan-diri', [GuruController::class, 'storePengembanganDiri'])->name('guru.pengembangan-diri.store');
    Route::get('/guru/pengembangan-diri/{id}/edit', [GuruController::class, 'editPengembanganDiri'])->name('guru.pengembangan-diri.edit');
    Route::put('/guru/pengembangan-diri/{id}', [GuruController::class, 'updatePengembanganDiri'])->name('guru.pengembangan-diri.update');
    Route::delete('/guru/pengembangan-diri/{id}', [GuruController::class, 'destroyPengembanganDiri'])->name('guru.pengembangan-diri.destroy');
    
    // Routes untuk tugas tambahan
    Route::post('/guru/{id}/tugas-tambahan', [GuruController::class, 'storeTugasTambahan'])->name('guru.tugas-tambahan.store');
    Route::put('/guru/tugas-tambahan/{id}', [GuruController::class, 'updateTugasTambahan'])->name('guru.tugas-tambahan.update');
    Route::delete('/guru/tugas-tambahan/{id}', [GuruController::class, 'destroyTugasTambahan'])->name('guru.tugas-tambahan.destroy');
});

// Tambahkan route GTK Non aktif baru dengan controller baru
Route::get('/gtk-nonaktif', [App\Http\Controllers\GtkNonAktifController::class, 'index'])->name('gtk.nonaktif.index');
Route::post('/gtk-nonaktif/aktivasi/{id}', [App\Http\Controllers\GtkNonAktifController::class, 'aktivasi'])->name('gtk.nonaktif.aktivasi');

// Route untuk Laporan Kerja
Route::middleware('check.laporan.kerja')->group(function () {
    Route::resource('laporan-kerja', App\Http\Controllers\LaporanKerjaController::class);
});

// Route untuk kenaikan kelas
Route::prefix('rombel/reguler')->name('rombel.reguler.')->middleware(['auth'])->group(function () {
    Route::get('/kenaikan', [RombelController::class, 'kenaikanView'])->name('kenaikan');
    Route::post('/kenaikan/individu', [RombelController::class, 'kenaikanKelasIndividu'])->name('kenaikan.individu');
    Route::post('/kenaikan/terpilih', [RombelController::class, 'kenaikanKelasTerpilih'])->name('kenaikan.terpilih');
});

// Route untuk alumni
Route::get('peserta-didik/alumni', [PesertaDidikController::class, 'alumni'])->name('peserta-didik.alumni');
Route::get('peserta-didik/alumni/{id}/edit', [PesertaDidikController::class, 'editAlumni'])->name('peserta-didik.alumni.edit');
Route::put('peserta-didik/alumni/{id}', [PesertaDidikController::class, 'updateAlumni'])->name('peserta-didik.alumni.update');
Route::delete('peserta-didik/alumni/{id}', [PesertaDidikController::class, 'destroyAlumni'])->name('peserta-didik.alumni.destroy');

/*
// Routes untuk absensi
Route::middleware(['auth'])->group(function () {
    // Tampilkan halaman absensi harian
    Route::get('/absensi/harian', [App\Http\Controllers\AbsensiController::class, 'harian'])->name('absensi.harian');
    
    // API untuk mendapatkan daftar siswa
    Route::get('/absensi/get-students', [App\Http\Controllers\AbsensiController::class, 'getStudents'])->name('absensi.get-students');
    
    // API untuk menyimpan absensi
    Route::post('/absensi/save', [App\Http\Controllers\AbsensiController::class, 'saveAttendance'])->name('absensi.save');
});
*/


// Tambahkan route untuk rekap admin dengan middleware
Route::get('/absensi/rekap-admin', [App\Http\Controllers\AbsensiController::class, 'rekapAdmin'])
    ->middleware(['auth', 'role:Administrator|Yayasan|Pengawas|Kepala Sekolah|Waka Kesiswaan|Waka Kurikulum', 'filter.unit'])
    ->name('absensi.rekap-admin');

Route::get('/absensi/get-rekap-admin', [App\Http\Controllers\AbsensiController::class, 'getRekapAdmin'])
    ->middleware(['auth', 'role:Administrator|Yayasan|Pengawas|Kepala Sekolah|Waka Kesiswaan|Waka Kurikulum', 'filter.unit'])
    ->name('absensi.get-rekap-admin');

Route::get('/absensi/get-kelas-by-guru', [App\Http\Controllers\AbsensiController::class, 'getKelasByGuru'])
    ->middleware(['auth', 'role:Administrator|Yayasan|Pengawas|Kepala Sekolah|Waka Kesiswaan|Waka Kurikulum', 'filter.unit'])
    ->name('absensi.get-kelas-by-guru');

// Route untuk ekstrakurikuler
Route::prefix('rombel')->name('rombel.')->middleware(['auth'])->group(function () {
    Route::get('/eksul', [RombelController::class, 'eksul'])->name('eksul');
    Route::post('/eksul', [RombelController::class, 'storeEksul'])->name('eksul.store');
    Route::get('/eksul/{id}', [RombelController::class, 'showEksul'])->name('eksul.show');
    Route::delete('/eksul/{id}', [RombelController::class, 'destroyEksul'])->name('eksul.destroy');
});

// Route untuk sarana dan prasarana
Route::middleware(['auth'])->group(function () {
    // Route resource untuk CRUD sarana
    Route::resource('sarana', SaranaController::class);
    
    // Route untuk melihat sarana berdasarkan lokasi
    Route::get('sarana/lokasi/{gedung}', [SaranaController::class, 'byLokasi'])->name('sarana.by-lokasi');
    
    // Route untuk melihat daftar lokasi
    Route::get('sarana-lokasi', [SaranaController::class, 'lokasiIndex'])->name('sarana.lokasi');
    
    // Route untuk menampilkan detail sarana dengan lokasi
    Route::get('sarana/showlokasi/{sarana}', [SaranaController::class, 'showLokasi'])->name('sarana.showlokasi');
});

// Tambahkan routes untuk Notifikasi
Route::middleware(['auth'])->group(function () {
    Route::get('/notifikasi', [App\Http\Controllers\NotifikasiController::class, 'index'])->name('notifikasi.index');
    Route::get('/notifikasi/{notifikasi}/mark-as-read', [App\Http\Controllers\NotifikasiController::class, 'markAsRead'])->name('notifikasi.mark-as-read');
    Route::get('/notifikasi/mark-all-as-read', [App\Http\Controllers\NotifikasiController::class, 'markAllAsRead'])->name('notifikasi.mark-all-as-read');
    Route::get('/notifikasi/count', [App\Http\Controllers\NotifikasiController::class, 'getUnreadCount'])->name('notifikasi.count');
    Route::delete('/notifikasi/{notifikasi}', [App\Http\Controllers\NotifikasiController::class, 'destroy'])->name('notifikasi.destroy');
    Route::delete('/notifikasi', [App\Http\Controllers\NotifikasiController::class, 'destroyAll'])->name('notifikasi.destroy-all');
    Route::get('/notifikasi/get-unread', [App\Http\Controllers\NotifikasiController::class, 'getUnreadNotifications'])->name('notifikasi.get-unread');
});

// Tambahkan routes untuk Laporan Kerusakan
Route::middleware(['auth', 'check.laporan.kerusakan'])->group(function () {
    Route::get('/laporan-kerusakan', [App\Http\Controllers\LaporanKerusakanController::class, 'index'])
        ->name('laporan-kerusakan.index');
    Route::get('/laporan-kerusakan/create', [App\Http\Controllers\LaporanKerusakanController::class, 'create'])
        ->name('laporan-kerusakan.create');
    Route::post('/laporan-kerusakan', [App\Http\Controllers\LaporanKerusakanController::class, 'store'])
        ->name('laporan-kerusakan.store');
    Route::get('/laporan-kerusakan/{laporanKerusakan}', [App\Http\Controllers\LaporanKerusakanController::class, 'show'])
        ->name('laporan-kerusakan.show');
    Route::get('/laporan-kerusakan/{laporanKerusakan}/edit', [App\Http\Controllers\LaporanKerusakanController::class, 'edit'])
        ->name('laporan-kerusakan.edit')
        ->middleware('permission:edit-laporan-kerusakan');
    Route::put('/laporan-kerusakan/{laporanKerusakan}', [App\Http\Controllers\LaporanKerusakanController::class, 'update'])
        ->name('laporan-kerusakan.update');
    Route::delete('/laporan-kerusakan/{laporanKerusakan}', [App\Http\Controllers\LaporanKerusakanController::class, 'destroy'])
        ->name('laporan-kerusakan.destroy')
         ->middleware('permission:delete-laporan-kerusakan');
    Route::post('/laporan-kerusakan/{laporanKerusakan}/proses', [App\Http\Controllers\LaporanKerusakanController::class, 'proses'])
        ->name('laporan-kerusakan.proses');
    Route::post('/laporan-kerusakan/{laporanKerusakan}/selesai', [App\Http\Controllers\LaporanKerusakanController::class, 'selesai'])
        ->name('laporan-kerusakan.selesai');
});

// Tambahkan route sederhana untuk testing
Route::get('/test-laporan', function() {
    return "Route berfungsi dengan baik";
});

// Perbaiki route laporan kerusakan
Route::get('/laporan-kerusakan', [App\Http\Controllers\LaporanKerusakanController::class, 'index'])
    ->name('laporan-kerusakan.index');

// Rute untuk Kalender Pendidikan (dalam grup middleware auth)
Route::middleware(['auth'])->group(function () {
    Route::prefix('kalender')->name('kalender.')->group(function () {
        Route::get('/events', [App\Http\Controllers\KalenderPendidikanController::class, 'getEvents'])->name('events');
        Route::post('/store', [App\Http\Controllers\KalenderPendidikanController::class, 'store'])->name('store');
        Route::get('/edit/{id}', [App\Http\Controllers\KalenderPendidikanController::class, 'edit'])->name('edit');
        Route::put('/update/{id}', [App\Http\Controllers\KalenderPendidikanController::class, 'update'])->name('update');
        Route::delete('/destroy/{id}', [App\Http\Controllers\KalenderPendidikanController::class, 'destroy'])->name('destroy');
    });
});

// Route untuk mendapatkan kelas berdasarkan tahun ajaran tugas tambahan
Route::get('/api/tahun-ajaran/{tahun_ajaran_id}/kelas', [App\Http\Controllers\GuruController::class, 'getKelasByTahunAjaran'])->name('api.tahun-ajaran.kelas');

// Route untuk pengembangan diri tendik
Route::post('gtk/tendik/{id}/pengembangan-diri', [TendikController::class, 'storePengembanganDiri'])->name('gtk.tendik.pengembangan-diri.store');
Route::put('gtk/tendik/pengembangan-diri/{id}', [TendikController::class, 'updatePengembanganDiri'])->name('gtk.tendik.pengembangan-diri.update');
Route::delete('gtk/tendik/pengembangan-diri/{id}', [TendikController::class, 'destroyPengembanganDiri'])->name('gtk.tendik.pengembangan-diri.destroy');

// Route untuk tugas tambahan tendik
Route::post('gtk/tendik/{id}/tugas-tambahan', [TendikController::class, 'storeTugasTambahan'])->name('gtk.tendik.tugas-tambahan.store');
Route::put('gtk/tendik/tugas-tambahan/{id}', [TendikController::class, 'updateTugasTambahan'])->name('gtk.tendik.tugas-tambahan.update');
Route::delete('gtk/tendik/tugas-tambahan/{id}', [TendikController::class, 'destroyTugasTambahan'])->name('gtk.tendik.tugas-tambahan.destroy');

// Route for features under development
Route::get('/under-development', function () {
    return view('website.under-development');
})->name('website.under-development');

// Example of redirecting a specific feature to the under development page
Route::get('/facilities/upcoming', function () {
    return redirect()->route('website.under-development');
});

// Route untuk fitur yang masih dalam pengembangan di admin panel
Route::get('/admin/under-development', function () {
    return view('admin.under-development');
})->name('admin.under-development')->middleware(['auth']);

// Contoh penggunaan: redirect fitur yang belum selesai ke halaman under-development
Route::get('/admin/fitur-baru', function () {
    return redirect()->route('admin.under-development');
})->middleware(['auth']);

// Routes untuk modul penilaian
Route::prefix('nilai')->name('nilai.')->group(function () {
    // Dashboard nilai
    Route::get('/', [App\Http\Controllers\NilaiController::class, 'index'])->name('index');
    
    // Laporan nilai
    Route::get('/laporan-kelas', [App\Http\Controllers\NilaiController::class, 'laporanKelas'])->name('laporan-kelas');
    Route::get('/cari-siswa', [App\Http\Controllers\NilaiController::class, 'cariSiswa'])->name('cari-siswa');
    Route::get('/laporan-siswa/{id}', [App\Http\Controllers\NilaiController::class, 'laporanSiswa'])->name('laporan-siswa');
});

// Routes untuk kompetensi dasar
Route::prefix('penilaian/kompetensi')->name('penilaian.kompetensi.')->group(function () {
    Route::get('/', [App\Http\Controllers\Nilai\KompetensiController::class, 'index'])->name('index');
    Route::get('/create', [App\Http\Controllers\Nilai\KompetensiController::class, 'create'])->name('create');
    Route::post('/', [App\Http\Controllers\Nilai\KompetensiController::class, 'store'])->name('store');
    Route::get('/{id}/edit', [App\Http\Controllers\Nilai\KompetensiController::class, 'edit'])->name('edit');
    Route::put('/{id}', [App\Http\Controllers\Nilai\KompetensiController::class, 'update'])->name('update');
    Route::delete('/{id}', [App\Http\Controllers\Nilai\KompetensiController::class, 'destroy'])->name('destroy');
});

// Routes untuk penilaian formatif
Route::prefix('penilaian/formatif')->name('penilaian.formatif.')->group(function () {
    Route::get('/', [App\Http\Controllers\PenilaianFormatifController::class, 'index'])->name('index');
    Route::get('/create', [App\Http\Controllers\PenilaianFormatifController::class, 'create'])->name('create');
    Route::post('/', [App\Http\Controllers\PenilaianFormatifController::class, 'store'])->name('store');
    Route::get('/{penilaianFormatif}', [App\Http\Controllers\PenilaianFormatifController::class, 'show'])->name('show');
    Route::get('/{penilaianFormatif}/edit', [App\Http\Controllers\PenilaianFormatifController::class, 'edit'])->name('edit');
    Route::put('/{penilaianFormatif}', [App\Http\Controllers\PenilaianFormatifController::class, 'update'])->name('update');
    Route::delete('/{penilaianFormatif}', [App\Http\Controllers\PenilaianFormatifController::class, 'destroy'])->name('destroy');
    Route::get('get-siswa-by-kelas', [App\Http\Controllers\PenilaianFormatifController::class, 'getSiswaByKelas'])->name('get-siswa-by-kelas');
    Route::post('store-batch', [App\Http\Controllers\PenilaianFormatifController::class, 'storeBatch'])->name('store-batch');
});

// Routes untuk penilaian sumatif
Route::prefix('penilaian/sumatif')->name('penilaian.sumatif.')->group(function () {
    Route::get('/', [App\Http\Controllers\PenilaianSumatifController::class, 'index'])->name('index');
    Route::get('/create', [App\Http\Controllers\PenilaianSumatifController::class, 'create'])->name('create');
    Route::post('/', [App\Http\Controllers\PenilaianSumatifController::class, 'store'])->name('store');
    Route::get('/{penilaianSumatif}', [App\Http\Controllers\PenilaianSumatifController::class, 'show'])->name('show');
    Route::get('/{penilaianSumatif}/edit', [App\Http\Controllers\PenilaianSumatifController::class, 'edit'])->name('edit');
    Route::put('/{penilaianSumatif}', [App\Http\Controllers\PenilaianSumatifController::class, 'update'])->name('update');
    Route::delete('/{penilaianSumatif}', [App\Http\Controllers\PenilaianSumatifController::class, 'destroy'])->name('destroy');
});

// Routes untuk penilaian proyek
Route::prefix('penilaian/proyek')->name('penilaian.proyek.')->group(function () {
    Route::get('/', [App\Http\Controllers\PenilaianProyekController::class, 'index'])->name('index');
    Route::get('/create', [App\Http\Controllers\PenilaianProyekController::class, 'create'])->name('create');
    Route::post('/', [App\Http\Controllers\PenilaianProyekController::class, 'store'])->name('store');
    Route::get('/{penilaianProyek}', [App\Http\Controllers\PenilaianProyekController::class, 'show'])->name('show');
    Route::get('/{penilaianProyek}/edit', [App\Http\Controllers\PenilaianProyekController::class, 'edit'])->name('edit');
    Route::put('/{penilaianProyek}', [App\Http\Controllers\PenilaianProyekController::class, 'update'])->name('update');
    Route::get('/{penilaianProyek}/nilai-siswa', [App\Http\Controllers\PenilaianProyekController::class, 'nilaiSiswa'])->name('nilaiSiswa');
    Route::post('/{penilaianProyek}/nilai-siswa', [App\Http\Controllers\PenilaianProyekController::class, 'simpanNilaiSiswa'])->name('simpanNilaiSiswa');
    Route::delete('/{penilaianProyek}', [App\Http\Controllers\PenilaianProyekController::class, 'destroy'])->name('destroy');
});

// Rute untuk Tujuan Pembelajaran
Route::prefix('penilaian')->name('penilaian.')->group(function () {
    Route::resource('tujuan-pembelajaran', \App\Http\Controllers\TujuanPembelajaranController::class);
});

// Rute untuk Cakapan Pembelajaran
Route::prefix('penilaian')->name('penilaian.')->group(function () {
    Route::resource('cakapan-pembelajaran', \App\Http\Controllers\CakapanPembelajaranController::class);
});

/*
// Grup route untuk pengaturan
Route::prefix('pengaturan')->name('pengaturan.')->middleware(['auth'])->group(function () {
    // Unit routes
    Route::get('/unit', [PengaturanController::class, 'unit'])
        ->name('unit')
        ->middleware('permission:view-unit');
    Route::post('/unit', [PengaturanController::class, 'storeUnit'])
        ->name('unit.store')
        ->middleware('permission:manage-unit');
    
    // Jenjang routes
    Route::get('/jenjang', [PengaturanController::class, 'jenjang'])
        ->name('jenjang')
        ->middleware('permission:view-jenjang');
    Route::post('/jenjang', [PengaturanController::class, 'storeJenjang'])
        ->name('jenjang.store')
        ->middleware('permission:manage-jenjang');
    
    // Gedung routes
    Route::get('/gedung', [PengaturanController::class, 'gedung'])
        ->name('gedung')
        ->middleware('permission:view-gedung');
    Route::post('/gedung', [PengaturanController::class, 'storeGedung'])
        ->name('gedung.store')
        ->middleware('permission:manage-gedung');
    
    // Kelas routes
    Route::get('/kelas', [PengaturanController::class, 'kelas'])
        ->name('kelas')
        ->middleware('permission:view-kelas');
    Route::post('/kelas', [PengaturanController::class, 'storeKelas'])
        ->name('kelas.store')
        ->middleware('permission:manage-kelas');
    
    // Mapel routes
    Route::get('/mapel', [PengaturanController::class, 'mapel'])
        ->name('mapel')
        ->middleware('permission:view-mapel');
    Route::post('/mapel', [PengaturanController::class, 'storeMapel'])
        ->name('mapel.store')
        ->middleware('permission:manage-mapel');
}); */

/*
// Grup route untuk Sarana dan Prasarana
Route::middleware(['auth'])->group(function () {
    // Data Sarpras routes
    Route::get('/sarana', [SarprasController::class, 'index'])
        ->name('sarana.index')
        ->middleware('permission:view-sarpras');
    
    Route::get('/sarana/create', [SarprasController::class, 'create'])
        ->name('sarana.create')
        ->middleware('permission:create-sarpras');
    
    Route::post('/sarana', [SarprasController::class, 'store'])
        ->name('sarana.store')
        ->middleware('permission:create-sarpras');
    
    Route::get('/sarana/{sarana}/edit', [SarprasController::class, 'edit'])
        ->name('sarana.edit')
        ->middleware('permission:edit-sarpras');
    
    Route::put('/sarana/{sarana}', [SarprasController::class, 'update'])
        ->name('sarana.update')
        ->middleware('permission:edit-sarpras');
    
    Route::delete('/sarana/{sarana}', [SarprasController::class, 'destroy'])
        ->name('sarana.destroy')
        ->middleware('permission:delete-sarpras');
    
    // Laporan Kerusakan routes
    Route::get('/laporan-kerusakan', [LaporanKerusakanController::class, 'index'])
        ->name('laporan-kerusakan.index')
        ->middleware('permission:view-laporan-kerusakan');
    
    Route::get('/laporan-kerusakan/create', [LaporanKerusakanController::class, 'create'])
        ->name('laporan-kerusakan.create')
        ->middleware('permission:create-laporan-kerusakan');
    
    Route::post('/laporan-kerusakan', [LaporanKerusakanController::class, 'store'])
        ->name('laporan-kerusakan.store')
        ->middleware('permission:create-laporan-kerusakan');
    
    // Kelola Laporan Kerusakan routes
    Route::get('/kelola-laporan-kerusakan', [LaporanKerusakanController::class, 'kelola'])
        ->name('kelola-laporan-kerusakan.index')
        ->middleware('permission:manage-laporan-kerusakan');
    
    Route::put('/laporan-kerusakan/{laporan}/approve', [LaporanKerusakanController::class, 'approve'])
        ->name('laporan-kerusakan.approve')
        ->middleware('permission:approve-laporan-kerusakan');
}); */









// Route untuk import dan export guru (di luar group route)
//Route::get('/gtk/guru/export-template', [App\Http\Controllers\GuruController::class, 'exportTemplate'])->name('gtk.guru.export-template');
Route::post('/gtk/guru/import', [App\Http\Controllers\GuruController::class, 'import'])->name('gtk.guru.import');

/* Route untuk guru
Route::prefix('gtk/guru')->name('gtk.guru.')->group(function () {
    Route::get('/', [App\Http\Controllers\GuruController::class, 'index'])->name('index');
    Route::get('/create', [App\Http\Controllers\GuruController::class, 'create'])->name('create');
    Route::post('/', [App\Http\Controllers\GuruController::class, 'store'])->name('store');
    Route::get('/{guru}/edit', [App\Http\Controllers\GuruController::class, 'edit'])->name('edit');
    Route::put('/{guru}', [App\Http\Controllers\GuruController::class, 'update'])->name('update');
    Route::delete('/{guru}', [App\Http\Controllers\GuruController::class, 'destroy'])->name('destroy');
    
    // Route untuk import dan export guru
    Route::get('/export-template', [App\Http\Controllers\GuruController::class, 'exportTemplate'])->name('export-template');
    Route::post('/import', [App\Http\Controllers\GuruController::class, 'import'])->name('import');
}); */

// Route khusus untuk export template
Route::get('/export/template/guru', [App\Http\Controllers\ExportTemplateController::class, 'guruTemplate'])
    ->name('export.template.guru');






