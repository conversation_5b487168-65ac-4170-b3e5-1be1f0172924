@extends('layouts.admin')

@section('title', 'Edit Jadwal Pelajaran')
 
@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Edit Jadwal Pelajaran</h3>
    </div>
    <div class="card-body">
        @if(session('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif

        <form action="{{ route('jadwal.update', $jadwal->id) }}" method="POST" id="formJadwal">
            @csrf
            @method('PUT')

            <!-- Tabel Form Input -->
            <div class="table-responsive mb-4">
                <table class="table table-bordered table-form-input">
                    <thead class="thead-light">
                        <tr>
                            <th colspan="2" class="text-center">
                                <i class="fas fa-edit"></i> Edit Jadwal Pelajaran
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td width="25%" class="font-weight-bold">
                                <i class="fas fa-th-large"></i> Pilih Layout
                            </td>
                            <td>
                                @php
                                    $user = auth()->user();
                                    $isAdmin = $user->hasRole('Administrator');

                                    // Mapping unit_id ke layout
                                    $unitToLayout = [
                                        1 => 'layout_tk',
                                        2 => 'layout_sd',
                                        3 => 'layout_smp',
                                        4 => 'layout_sma'
                                    ];

                                    // Tentukan layout default berdasarkan unit_id user
                                    $defaultLayout = isset($unitToLayout[$user->unit_id]) ? $unitToLayout[$user->unit_id] : '';
                                @endphp

                                @if($isAdmin)
                                    {{-- Administrator dapat memilih semua layout --}}
                                    <select name="layout_type" class="form-control" required>
                                        <option value="">Pilih Layout</option>
                                        @foreach($layouts as $key => $value)
                                            <option value="{{ $key }}" {{ old('layout_type', $jadwal->layout_type) == $key ? 'selected' : '' }}>
                                                {{ $value }}
                                            </option>
                                        @endforeach
                                    </select>
                                @else
                                    {{-- User non-admin hanya dapat menggunakan layout sesuai unit_id --}}
                                    <select name="layout_type" class="form-control" required {{ !$isAdmin ? 'disabled' : '' }}>
                                        @foreach($layouts as $key => $value)
                                            <option value="{{ $key }}"
                                                {{ ($key == $defaultLayout) || (old('layout_type', $jadwal->layout_type) == $key) ? 'selected' : '' }}
                                                {{ $key != $defaultLayout ? 'disabled' : '' }}>
                                                {{ $value }}
                                            </option>
                                        @endforeach
                                    </select>
                                    {{-- Tambahkan hidden input untuk memastikan nilai tetap terkirim meskipun select disabled --}}
                                    <input type="hidden" name="layout_type" value="{{ old('layout_type', $jadwal->layout_type) ?? $defaultLayout }}">
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">
                                <i class="fas fa-school"></i> Kelas
                            </td>
                            <td>
                                <select name="kelas_id" class="form-control @error('kelas_id') is-invalid @enderror" required>
                                    <option value="">Pilih Kelas</option>
                                    @php
                                        // Ambil tahun ajaran aktif
                                        $tahunAjaranAktif = \App\Models\TahunAjaran::where('aktif', true)->first();

                                        // Filter kelas berdasarkan tahun ajaran aktif
                                        $kelasFiltered = $kelas->filter(function($k) use ($tahunAjaranAktif) {
                                            return $k->tahun_ajaran == ($tahunAjaranAktif ? $tahunAjaranAktif->nama : null);
                                        });
                                    @endphp
                                    @foreach($kelasFiltered as $k)
                                        <option value="{{ $k->id }}" {{ old('kelas_id', $jadwal->kelas_id) == $k->id ? 'selected' : '' }}>{{ $k->nama }}</option>
                                    @endforeach
                                </select>
                                @error('kelas_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">
                                <i class="fas fa-edit"></i> Nama Kelas (Teks)
                            </td>
                            <td>
                                <input type="text" name="nama_kelas_text" class="form-control @error('nama_kelas_text') is-invalid @enderror"
                                       value="{{ old('nama_kelas_text', $jadwal->nama_kelas_text) }}" required placeholder="Contoh: VII A">
                                @error('nama_kelas_text')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">
                                <i class="fas fa-user-tie"></i> Wali Kelas
                            </td>
                            <td>
                                <select name="wali_kelas" class="form-control @error('wali_kelas') is-invalid @enderror" required>
                                    <option value="">Pilih Wali Kelas</option>
                                    @php
                                        // Ambil tahun ajaran aktif
                                        $tahunAjaranAktif = \App\Models\TahunAjaran::where('aktif', true)->first();

                                        // Ambil data guru dengan tugas tambahan Wali Kelas atau Guru Kelas
                                        // pada tahun ajaran yang aktif
                                        $waliKelas = DB::table('guru_tugastambahan')
                                            ->whereIn('tugas_tambahan', ['Wali Kelas', 'Guru Kelas'])
                                            ->where('tahun_ajaran_id', $tahunAjaranAktif ? $tahunAjaranAktif->id : null)
                                            ->orderBy('nama_guru')
                                            ->get();
                                    @endphp
                                    @foreach($waliKelas as $wali)
                                        <option value="{{ $wali->nama_guru }}" {{ old('wali_kelas', $jadwal->wali_kelas) == $wali->nama_guru ? 'selected' : '' }}>{{ $wali->nama_guru }}</option>
                                    @endforeach
                                </select>
                                @error('wali_kelas')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">
                                <i class="fas fa-calendar-alt"></i> Tahun Ajaran
                            </td>
                            <td>
                                <select name="tahun_ajaran" class="form-control @error('tahun_ajaran') is-invalid @enderror" required>
                                    <option value="">Pilih Tahun Ajaran</option>
                                    @foreach($tahunAjaranList as $ta)
                                        <option value="{{ $ta }}" {{ old('tahun_ajaran', $jadwal->tahun_ajaran) == $ta ? 'selected' : '' }}>{{ $ta }}</option>
                                    @endforeach
                                </select>
                                @error('tahun_ajaran')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div id="jadwalContainer" class="mt-4">
                <!-- Jadwal akan di-render di sini menggunakan JavaScript -->
            </div>

            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary btn-submit">
                    <i class="fas fa-save"></i> Simpan Perubahan Jadwal
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal untuk memilih mata pelajaran -->
<div class="modal fade" id="mapelModal" tabindex="-1" role="dialog" aria-labelledby="mapelModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="mapelModalLabel">Pilih Mata Pelajaran</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>Cari Mata Pelajaran:</label>
                    <input type="text" id="searchMapel" class="form-control" placeholder="Ketik nama mata pelajaran atau guru...">
                </div>
                <div class="row" id="mapelOptions">
                    <!-- Kosong untuk pilihan -->
                    <div class="col-12 mb-2">
                        <div class="mapel-option empty-option" data-value="" onclick="selectMapel(this)">
                            <div class="mapel-name">-- Kosong --</div>
                        </div>
                    </div>

                    <!-- Mata Pelajaran -->
                    @foreach($mataPelajaran as $mp)
                    <div class="col-12 mb-2 mapel-item" data-search="{{ strtolower($mp->nama_mapel . ' ' . ($mp->pengajar->name ?? 'Belum ditentukan')) }}">
                        <div class="mapel-option" data-value="{{ $mp->id }}" data-mapel="{{ $mp->nama_mapel }}" data-guru="{{ $mp->pengajar->name ?? 'Belum ditentukan' }}" onclick="selectMapel(this)">
                            <div class="mapel-name">{{ $mp->nama_mapel }}</div>
                            <div class="guru-name">{{ $mp->pengajar->name ?? 'Belum ditentukan' }}</div>
                        </div>
                    </div>
                    @endforeach

                    <!-- Kegiatan Khusus -->
                    @foreach($specialEvents as $event)
                    <div class="col-12 mb-2 kegiatan-item" data-search="{{ strtolower($event['nama']) }}">
                        <div class="mapel-option kegiatan-khusus" data-value="{{ $event['id'] }}" data-mapel="{{ $event['nama'] }}" onclick="selectMapel(this)">
                            <div class="mapel-name">{{ $event['nama'] }}</div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('css')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap4-theme@1.0.0/dist/select2-bootstrap4.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<style>
    /* Styling untuk tabel form input */
    .table-form-input {
        background-color: #f8f9fa;
    }

    .table-form-input thead th {
        background-color: #e9ecef;
        border-color: #dee2e6;
        font-weight: 600;
    }

    .table-form-input tbody td {
        vertical-align: middle;
        border-color: #dee2e6;
    }

    .table-form-input tbody td:first-child {
        background-color: #f1f3f4;
        font-weight: 500;
    }

    /* Styling untuk tabel jadwal */
    .jadwal-table {
        margin-top: 20px;
        font-size: 0.9rem;
    }

    .jadwal-table thead th {
        background-color: #007bff;
        color: white;
        text-align: center;
        font-weight: 600;
        border-color: #0056b3;
        padding: 12px 8px;
        vertical-align: middle;
    }

    .jadwal-table tbody td {
        vertical-align: middle;
        border-color: #dee2e6;
        padding: 8px;
    }

    /* Kolom jam (kolom pertama) */
    .jadwal-table .jam-cell {
        background-color: #e3f2fd;
        font-weight: 600;
        text-align: center;
        width: 100px;
        min-width: 100px;
        max-width: 100px;
        white-space: nowrap;
        font-size: 0.85rem;
    }

    /* Kolom mata pelajaran */
    .jadwal-table .mapel-cell {
        background-color: #ffffff;
        min-width: 180px;
        position: relative;
        padding: 4px;
    }

    /* Styling untuk display mata pelajaran dan guru */
    .mapel-display {
        padding: 8px;
        border-radius: 4px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        min-height: 45px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .mapel-display:hover {
        background-color: #e9ecef;
        border-color: #adb5bd;
    }

    .mapel-display.empty {
        background-color: #ffffff;
        border: 2px dashed #dee2e6;
        color: #6c757d;
        font-style: italic;
        align-items: center;
        justify-content: center;
        min-height: 40px;
    }

    .mapel-display.empty:hover {
        border-color: #007bff;
        color: #007bff;
    }

    .mapel-name {
        font-weight: 600;
        font-size: 0.85rem;
        color: #495057;
        line-height: 1.2;
        margin-bottom: 2px;
    }

    .guru-name {
        font-size: 0.75rem;
        color: #6c757d;
        line-height: 1.1;
    }

    .kegiatan-khusus {
        background-color: #fff3cd !important;
        border-color: #ffeaa7 !important;
        color: #856404;
    }

    .kegiatan-khusus .mapel-name {
        color: #856404;
    }

    /* Styling untuk modal mata pelajaran */
    .mapel-option {
        padding: 12px;
        border-radius: 6px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        cursor: pointer;
        transition: all 0.2s ease;
        min-height: 60px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .mapel-option:hover {
        background-color: #e9ecef;
        border-color: #007bff;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .mapel-option.empty-option {
        background-color: #ffffff;
        border: 2px dashed #dee2e6;
        color: #6c757d;
        font-style: italic;
        align-items: center;
        justify-content: center;
        min-height: 50px;
    }

    .mapel-option.empty-option:hover {
        border-color: #dc3545;
        color: #dc3545;
    }

    .mapel-option.kegiatan-khusus {
        background-color: #fff3cd;
        border-color: #ffeaa7;
    }

    .mapel-option.kegiatan-khusus:hover {
        background-color: #ffeaa7;
        border-color: #ffcd39;
    }

    #mapelModal .modal-body {
        max-height: 400px;
        overflow-y: auto;
    }

    #searchMapel {
        margin-bottom: 15px;
    }

    /* Styling untuk select dalam tabel */
    .jadwal-table .form-control-sm {
        font-size: 0.8rem;
        padding: 4px 8px;
        height: auto;
        min-height: 32px;
    }

    /* Hover effect untuk sel */
    .jadwal-table .mapel-cell:hover {
        background-color: #f8f9fa;
    }

    /* Icon styling */
    .table i {
        margin-right: 8px;
        color: #6c757d;
    }

    .thead-light th i {
        color: #495057;
    }

    /* Form control dalam tabel */
    .table .form-control {
        border-radius: 4px;
        border: 1px solid #ced4da;
    }

    .table .form-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* Button styling */
    .btn-submit {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
        padding: 12px 30px;
        font-weight: 600;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
        transition: all 0.3s ease;
    }

    .btn-submit:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
    }

    /* Styling untuk Select2 dalam tabel jadwal */
    .jadwal-table .select2-container {
        width: 100% !important;
    }

    .jadwal-table .select2-container .select2-selection--single {
        height: 32px !important;
        border: 1px solid #ced4da !important;
        border-radius: 4px !important;
    }

    .jadwal-table .select2-container .select2-selection--single .select2-selection__rendered {
        line-height: 30px !important;
        padding-left: 8px !important;
        font-size: 0.8rem !important;
    }

    .jadwal-table .select2-container .select2-selection--single .select2-selection__arrow {
        height: 30px !important;
        right: 5px !important;
    }

    /* Zebra striping untuk baris */
    .jadwal-table tbody tr:nth-child(even) .mapel-cell {
        background-color: #f8f9fa;
    }

    .jadwal-table tbody tr:nth-child(odd) .mapel-cell {
        background-color: #ffffff;
    }

    /* Highlight untuk jam istirahat */
    .jadwal-table .jam-istirahat {
        background-color: #fff3cd !important;
        color: #856404;
    }

    /* Responsive untuk tabel jadwal */
    @media (max-width: 768px) {
        .jadwal-table {
            font-size: 0.8rem;
        }

        .jadwal-table .jam-cell {
            width: 80px;
            min-width: 80px;
            max-width: 80px;
            font-size: 0.75rem;
        }

        .jadwal-table .mapel-cell {
            min-width: 120px;
        }

        .jadwal-table .form-control-sm {
            font-size: 0.75rem;
            padding: 3px 6px;
        }
    }
</style>
@stop

@section('js')
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // Show success/error messages
    @if(session('success'))
        Swal.fire({
            icon: 'success',
            title: 'Berhasil!',
            text: '{{ session('success') }}',
            timer: 3000,
            showConfirmButton: false,
            toast: true,
            position: 'top-end'
        });
    @endif

    @if(session('error'))
        @php
            $errorMessage = session('error');
            $isDetailedError = strpos($errorMessage, 'BENTROK JADWAL TERDETEKSI!') !== false;
        @endphp

        @if($isDetailedError)
            Swal.fire({
                icon: 'error',
                title: '⚠️ BENTROK JADWAL TERDETEKSI!',
                html: `<div style="text-align: left; white-space: pre-line;">{{ str_replace(["\n", '"'], ['<br>', '&quot;'], $errorMessage) }}</div>`,
                confirmButtonText: 'Saya Mengerti',
                confirmButtonColor: '#dc3545',
                width: '600px'
            });
        @else
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: '{{ $errorMessage }}',
                confirmButtonText: 'OK'
            });
        @endif
    @endif

    @if($errors->any())
        let errorMessages = '';
        @foreach($errors->all() as $error)
            errorMessages += '{{ $error }}\n';
        @endforeach

        Swal.fire({
            icon: 'error',
            title: 'Terdapat Kesalahan',
            text: errorMessages,
            confirmButtonText: 'OK'
        });
    @endif
    // Definisi layouts
    const layouts = {
        layout_tk: { // TK - 10 Jam Pelajaran
            jamPelajaran: 10,
            waktu: [
                { mulai: '07:30', selesai: '08:00' },
                { mulai: '08:00', selesai: '08:30' },
                { mulai: '08:30', selesai: '09:00' },
                { mulai: '09:00', selesai: '09:30' },
                { mulai: '09:30', selesai: '09:45' }, // Istirahat
                { mulai: '09:45', selesai: '10:15' },
                { mulai: '10:15', selesai: '10:45' },
                { mulai: '10:45', selesai: '11:15' },
                { mulai: '11:15', selesai: '11:45' },
                { mulai: '11:45', selesai: '12:15' }
            ]
        },
        layout_sd: { // SD - 14 Jam Pelajaran
            jamPelajaran: 14,
            waktu: [
                { mulai: '07:15', selesai: '07:30' },
                { mulai: '07:30', selesai: '08:05' },
                { mulai: '08:05', selesai: '08:40' },
                { mulai: '08:40', selesai: '09:15' },
                { mulai: '09:15', selesai: '09:45' },
                { mulai: '09:45', selesai: '10:00' }, // Istirahat 1
                { mulai: '10:00', selesai: '10:30' },
                { mulai: '10:30', selesai: '11:00' },
                { mulai: '11:00', selesai: '11:30' },
                { mulai: '11:30', selesai: '12:00' },
                { mulai: '12:00', selesai: '12:25' }, // Istirahat 2
                { mulai: '12:25', selesai: '12:55' },
                { mulai: '12:55', selesai: '13:25' },
                { mulai: '13:25', selesai: '13:55' }
            ]
        },
        layout_smp: { // SMP - 16 Jam Pelajaran
            jamPelajaran: 16,
            waktu: [
                { mulai: '07:15', selesai: '07:30' },
                { mulai: '07:30', selesai: '08:10' },
                { mulai: '08:10', selesai: '08:45' },
                { mulai: '08:45', selesai: '09:20' },
                { mulai: '09:20', selesai: '09:35' }, // Istirahat 1
                { mulai: '09:35', selesai: '10:10' },
                { mulai: '10:10', selesai: '10:45' },
                { mulai: '10:45', selesai: '11:20' },
                { mulai: '11:20', selesai: '11:55' },
                { mulai: '11:55', selesai: '12:45' }, // Istirahat 2
                { mulai: '12:45', selesai: '13:20' },
                { mulai: '13:20', selesai: '13:55' },
                { mulai: '13:55', selesai: '14:30' },
                { mulai: '14:30', selesai: '14:45' }, // Istirahat 3
                { mulai: '14:45', selesai: '15:20' },
                { mulai: '15:20', selesai: '15:55' }
            ]
        },
        layout_sma: { // SMA - 18 Jam Pelajaran
            jamPelajaran: 18,
            waktu: [
                { mulai: '07:15', selesai: '07:30' },
                { mulai: '07:30', selesai: '08:10' },
                { mulai: '08:10', selesai: '08:50' },
                { mulai: '08:50', selesai: '09:30' },
                { mulai: '09:30', selesai: '09:45' }, // Istirahat 1
                { mulai: '09:45', selesai: '10:25' },
                { mulai: '10:25', selesai: '11:05' },
                { mulai: '11:05', selesai: '11:45' },
                { mulai: '11:45', selesai: '12:25' },
                { mulai: '12:25', selesai: '13:15' }, // Istirahat 2
                { mulai: '13:15', selesai: '13:55' },
                { mulai: '13:55', selesai: '14:35' },
                { mulai: '14:35', selesai: '15:15' },
                { mulai: '15:15', selesai: '15:30' }, // Istirahat 3
                { mulai: '15:30', selesai: '16:10' },
                { mulai: '16:10', selesai: '16:50' },
                { mulai: '16:50', selesai: '17:30' },
                { mulai: '17:30', selesai: '18:10' }
            ]
        }
    };
    
    // Variabel untuk menyimpan layout terakhir yang dipilih
    let lastSelectedLayout = '{{ old('layout_type', $jadwal->layout_type) }}';
    
    // Variabel untuk menyimpan seleksi saat ini
    let currentSelections = {};
    
    // Fungsi untuk menyimpan seleksi saat ini
    function saveCurrentSelections() {
        // Simpan seleksi jadwal
        $('select[name$="[mapel_id]"]').each(function() {
            const name = $(this).attr('name');
            const value = $(this).val();
            if (value) {
                currentSelections[name] = value;
            }
        });
        
        // Simpan juga nilai form utama
        currentSelections['kelas_id'] = $('select[name="kelas_id"]').val();
        currentSelections['nama_kelas_text'] = $('input[name="nama_kelas_text"]').val();
        currentSelections['wali_kelas'] = $('select[name="wali_kelas"]').val();
        currentSelections['tahun_ajaran'] = $('select[name="tahun_ajaran"]').val();
    }
    
    // Fungsi untuk mengembalikan seleksi yang disimpan
    function restoreSelections() {
        for (const name in currentSelections) {
            $(`select[name="${name}"]`).val(currentSelections[name]);
            $(`input[name="${name}"]`).val(currentSelections[name]);
        }
    }
    
    // Fungsi untuk setup handler perubahan mapel
    function setupMapelChangeHandler() {
        $('select[name$="[mapel_id]"]').change(function() {
            checkBentrok($(this));
        });
    }
    
    // Fungsi untuk render jadwal berdasarkan layout yang dipilih
    function renderJadwalTable(layoutType) {
        console.log("Rendering jadwal table for layout:", layoutType);

        if (!layoutType) {
            console.error("Layout type tidak valid");
            return;
        }

        // Simpan seleksi saat ini sebelum render ulang
        saveCurrentSelections();

        const layout = layouts[layoutType];
        if (!layout) {
            console.error("Layout tidak ditemukan untuk type:", layoutType);
            return;
        }

        console.log("Layout ditemukan:", layout);

        const hari = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat'];

        // Buat HTML untuk tabel jadwal dengan format baru
        let html = `
            <div class="mt-4">
                <h4 class="text-primary mb-3">
                    <i class="fas fa-table"></i> Jadwal Pelajaran
                </h4>
                <div class="table-responsive">
                    <table class="table table-bordered jadwal-table">
                        <thead>
                            <tr>
                                <th width="100px" class="text-center">Jam</th>`;

        // Header hari
        hari.forEach(h => {
            html += `<th class="text-center">${h}</th>`;
        });

        html += `
                            </tr>
                        </thead>
                        <tbody>`;

        // Baris untuk setiap waktu
        layout.waktu.forEach((w, wIndex) => {
            html += `
                <tr>
                    <td class="jam-cell font-weight-bold text-center">${w.mulai}-${w.selesai}</td>`;

            // Kolom untuk setiap hari
            hari.forEach((h, hIndex) => {
                const selectName = `jadwal[${hIndex}_${wIndex}][mapel_id]`;

                html += `
                    <td class="mapel-cell">
                        <div class="mapel-display empty" data-hari="${h}" data-waktu-mulai="${w.mulai}" data-waktu-selesai="${w.selesai}" onclick="openMapelModal(this)">
                            <span>Klik untuk pilih</span>
                        </div>
                        <input type="hidden" name="${selectName}" value="" data-hari="${h}" data-waktu-mulai="${w.mulai}" data-waktu-selesai="${w.selesai}">
                        <input type="hidden" name="jadwal[${hIndex}_${wIndex}][hari]" value="${h}">
                        <input type="hidden" name="jadwal[${hIndex}_${wIndex}][waktu_mulai]" value="${w.mulai}">
                        <input type="hidden" name="jadwal[${hIndex}_${wIndex}][waktu_selesai]" value="${w.selesai}">
                    </td>`;
            });

            html += `</tr>`;
        });

        html += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        console.log("Rendering HTML to jadwalContainer");
        $('#jadwalContainer').html(html);

        // Setup event handlers untuk mapel display terlebih dahulu
        console.log("Setting up mapel display handlers");
        setupMapelDisplayHandlers();

        // Isi jadwal dari data yang ada setelah DOM siap
        console.log("Calling fillJadwalFromData");
        fillJadwalFromData();

        // Kembalikan seleksi yang disimpan
        console.log("Restoring selections");
        restoreSelections();

        // Setup handler untuk perubahan mapel
        console.log("Setting up mapel change handler");
        setupMapelChangeHandler();
        
        // Initialize Select2 for mata pelajaran dropdowns
        $('select[name^="jadwal"][name$="[mapel_id]"]').select2({
            theme: 'bootstrap4',
            placeholder: "Pilih Mata Pelajaran",
            allowClear: true,
            width: '100%',
            dropdownParent: $('#jadwalContainer'),
            language: {
                searching: function() {
                    return "Ketik nama guru atau mata pelajaran...";
                }
            }
        });
    }
    
    // Fungsi untuk mengisi jadwal dari data yang ada
    function fillJadwalFromData() {
        console.log("Filling jadwal data...");

        // Data detail jadwal dari database
        const detailJadwal = @json($jadwal->detailJadwal);
        console.log("Detail jadwal:", detailJadwal);

        if (!detailJadwal || detailJadwal.length === 0) {
            console.log("Tidak ada detail jadwal yang ditemukan");
            return;
        }

        // Tunggu sebentar untuk memastikan DOM sudah siap
        setTimeout(() => {
            // Iterasi setiap detail jadwal dan cari mapel display element yang sesuai
            detailJadwal.forEach((detail, index) => {
                console.log(`Processing detail ${index}:`, detail);

                // Format waktu untuk mencocokkan dengan yang ada di DOM
                const waktuMulai = detail.waktu_mulai.substring(0, 5); // HH:MM
                const waktuSelesai = detail.waktu_selesai.substring(0, 5); // HH:MM

                // Cari mapel display element berdasarkan hari dan waktu
                const mapelElement = document.querySelector(`[data-hari="${detail.hari}"][data-waktu-mulai="${waktuMulai}"][data-waktu-selesai="${waktuSelesai}"]`);

                if (mapelElement) {
                    const hiddenInput = mapelElement.parentElement.querySelector('input[type="hidden"][name*="[mapel_id]"]');
                    let value = '';
                    let mapelName = '';
                    let guruName = '';

                    // Tentukan nilai yang akan diset
                    if (detail.is_istirahat == 1 || detail.is_istirahat === true) {
                        value = 'istirahat';
                        mapelName = 'Istirahat';
                    } else if (detail.keterangan && detail.keterangan.trim() !== '') {
                        // Cari special event yang sesuai
                        const keterangan = detail.keterangan.toLowerCase().trim();
                        if (keterangan.includes('upacara')) {
                            value = 'upacara';
                            mapelName = 'Upacara';
                        } else if (keterangan.includes('kebaktian')) {
                            value = 'kebaktian';
                            mapelName = 'Kebaktian';
                        } else if (keterangan.includes('istirahat')) {
                            value = 'istirahat';
                            mapelName = 'Istirahat';
                        } else if (keterangan.includes('ekstra')) {
                            value = 'ekstra';
                            mapelName = 'Ekstrakurikuler';
                        } else if (keterangan.includes('pramuka')) {
                            value = 'pramuka';
                            mapelName = 'Pramuka';
                        } else {
                            // Keterangan custom
                            value = 'custom_' + index;
                            mapelName = detail.keterangan;
                        }
                    } else if (detail.mata_pelajaran_id && detail.mata_pelajaran) {
                        value = detail.mata_pelajaran_id.toString();
                        mapelName = detail.mata_pelajaran.nama_mapel || 'Mata Pelajaran';
                        guruName = (detail.mata_pelajaran.pengajar && detail.mata_pelajaran.pengajar.name) ? detail.mata_pelajaran.pengajar.name : 'Belum ditentukan';
                    }

                    console.log(`Setting mapel display for ${detail.hari} ${waktuMulai}-${waktuSelesai} to value "${value}", mapel: "${mapelName}", guru: "${guruName}"`);

                    // Set nilai pada hidden input
                    if (hiddenInput) {
                        hiddenInput.value = value;
                        console.log(`Hidden input value set to: ${value}`);
                    }

                    // Update tampilan mapel display
                    if (!value || value === '') {
                        mapelElement.innerHTML = '<span>Klik untuk pilih</span>';
                        mapelElement.className = 'mapel-display empty';
                    } else if (guruName && guruName !== 'Belum ditentukan') {
                        // Mata pelajaran dengan guru
                        mapelElement.innerHTML = `
                            <div class="mapel-name">${mapelName}</div>
                            <div class="guru-name">${guruName}</div>
                        `;
                        mapelElement.className = 'mapel-display';
                    } else if (value.includes('istirahat') || value.includes('upacara') || value.includes('kebaktian') || value.includes('ekstra') || value.includes('pramuka')) {
                        // Kegiatan khusus
                        mapelElement.innerHTML = `
                            <div class="mapel-name">${mapelName}</div>
                        `;
                        mapelElement.className = 'mapel-display kegiatan-khusus';
                    } else {
                        // Mata pelajaran tanpa guru atau keterangan custom
                        mapelElement.innerHTML = `
                            <div class="mapel-name">${mapelName}</div>
                            ${guruName ? `<div class="guru-name">${guruName}</div>` : ''}
                        `;
                        mapelElement.className = 'mapel-display';
                    }

                    console.log(`Mapel display updated successfully for ${detail.hari} ${waktuMulai}-${waktuSelesai}`);
                } else {
                    console.log(`Mapel display element not found for ${detail.hari} ${waktuMulai}-${waktuSelesai}`);
                    // Debug: cari semua element dengan hari yang sama
                    const elementsWithSameDay = document.querySelectorAll(`[data-hari="${detail.hari}"]`);
                    console.log(`Found ${elementsWithSameDay.length} elements with hari ${detail.hari}`);
                    elementsWithSameDay.forEach((el, idx) => {
                        console.log(`Element ${idx}: waktu ${el.getAttribute('data-waktu-mulai')}-${el.getAttribute('data-waktu-selesai')}`);
                    });
                }
            });
        }, 100); // Delay 100ms untuk memastikan DOM sudah siap
    }
    
    // Handler untuk perubahan layout
    $('select[name="layout_type"]').change(function() {
        const layoutType = $(this).val();
        renderJadwalTable(layoutType);
        lastSelectedLayout = layoutType;
    });
    
    // Inisialisasi jadwal saat halaman dimuat
    // Ambil layout dari data jadwal yang ada
    const currentLayout = '{{ old('layout_type', $jadwal->layout_type) }}';

    // Untuk user non-admin, ambil nilai default layout dari hidden input
    const defaultLayout = $('input[type="hidden"][name="layout_type"]').val() || $('select[name="layout_type"]').val() || currentLayout;

    console.log("Current layout:", currentLayout);
    console.log("Default layout:", defaultLayout);
    console.log("Available layouts:", Object.keys(layouts));

    // Pastikan ada layout yang valid
    const layoutToUse = (defaultLayout && layouts[defaultLayout]) ? defaultLayout : Object.keys(layouts)[0];

    if (layoutToUse) {
        console.log("Using layout:", layoutToUse);

        // Set nilai select layout terlebih dahulu
        if ($('select[name="layout_type"]').length) {
            $('select[name="layout_type"]').val(layoutToUse);
        }

        // Render jadwal dengan layout yang dipilih
        renderJadwalTable(layoutToUse);
        lastSelectedLayout = layoutToUse;

        console.log("Layout initialization completed");
    } else {
        console.error("No valid layout found!");
    }
    
    // Tambahkan event handler untuk menyimpan nilai form utama saat berubah
    $('select[name="kelas_id"], input[name="nama_kelas_text"], select[name="wali_kelas"], select[name="tahun_ajaran"]').change(function() {
        saveCurrentSelections();
    });
    
    // Initialize Select2 for other dropdowns
    $('select[name="kelas_id"], select[name="wali_kelas"], select[name="tahun_ajaran"]').select2({
        theme: 'bootstrap4',
        width: '100%'
    });

    // Pastikan nilai form utama sudah terisi dengan benar
    console.log("Setting form values from database...");

    // Set nilai kelas_id
    const kelasId = '{{ old('kelas_id', $jadwal->kelas_id) }}';
    if (kelasId) {
        $('select[name="kelas_id"]').val(kelasId).trigger('change');
        console.log("Kelas ID set to:", kelasId);
    }

    // Set nilai wali_kelas
    const waliKelas = '{{ old('wali_kelas', $jadwal->wali_kelas) }}';
    if (waliKelas) {
        $('select[name="wali_kelas"]').val(waliKelas).trigger('change');
        console.log("Wali Kelas set to:", waliKelas);
    }

    // Set nilai tahun_ajaran
    const tahunAjaran = '{{ old('tahun_ajaran', $jadwal->tahun_ajaran) }}';
    if (tahunAjaran) {
        $('select[name="tahun_ajaran"]').val(tahunAjaran).trigger('change');
        console.log("Tahun Ajaran set to:", tahunAjaran);
    }

    // Set nilai nama_kelas_text
    const namaKelasText = '{{ old('nama_kelas_text', $jadwal->nama_kelas_text) }}';
    if (namaKelasText) {
        $('input[name="nama_kelas_text"]').val(namaKelasText);
        console.log("Nama Kelas Text set to:", namaKelasText);
    }
    
    // Tambahkan validasi form
    $('#formJadwal').submit(function(e) {
        e.preventDefault(); // Prevent default submission

        // Validasi yang sudah ada
        const jadwalPerHari = {};
        const hari = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat'];
        hari.forEach(h => jadwalPerHari[h] = 0);

        let error = false;
        let bentrokFound = false;

        // Cek jumlah mapel per hari
        $(this).find('input[type="hidden"][name$="[mapel_id]"]').each(function() {
            const name = $(this).attr('name');
            const hari = $(`input[name="${name.replace('[mapel_id]', '[hari]')}"]`).val();
            const value = $(this).val();
            if (value && !value.includes('istirahat') && !value.includes('upacara') &&
                !value.includes('kebaktian') && !value.includes('ekstra') && !value.includes('pramuka')) {
                jadwalPerHari[hari]++;
            }

            // Cek bentrok untuk setiap mapel
            const mapelElement = $(this).parent().find('.mapel-display')[0];
            if (mapelElement && checkBentrokByElement(mapelElement)) {
                bentrokFound = true;
            }
        });

        // Validasi minimal 1 mapel per hari
        hari.forEach(h => {
            if (jadwalPerHari[h] < 1) {
                Swal.fire({
                    title: 'Error!',
                    text: `Hari ${h} harus memiliki minimal 1 mata pelajaran`,
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                error = true;
            }
        });

        if (!error && !bentrokFound) {
            // Jika tidak ada error dan bentrok, submit form
            this.submit();
        }
    });
});

function checkBentrok(changedSelect) {
    const selectedMapelId = changedSelect.val();
    if (!selectedMapelId || selectedMapelId.includes('istirahat') ||
        selectedMapelId.includes('upacara') || selectedMapelId.includes('kebaktian') ||
        selectedMapelId.includes('ekstra') || selectedMapelId.includes('pramuka')) {
        return false;
    }

    const hari = changedSelect.data('hari');
    const waktuMulai = changedSelect.data('waktu-mulai');
    const waktuSelesai = changedSelect.data('waktu-selesai');
    let bentrok = false;

    // Cari informasi mata pelajaran dan guru
    const mataPelajaran = @json($mataPelajaran);
    const selectedMapel = mataPelajaran.find(m => m.id == selectedMapelId);
    const namaMapel = selectedMapel ? selectedMapel.nama_mapel : 'Mata Pelajaran';
    const namaGuru = selectedMapel && selectedMapel.pengajar ? selectedMapel.pengajar.name : 'Guru tidak diketahui';

    // Cek bentrok dengan jadwal yang sudah ada di database
    checkBentrokWithDatabase(selectedMapelId, hari, waktuMulai, waktuSelesai, namaMapel, namaGuru);

    // Cek bentrok dengan jadwal lain pada hari yang sama berdasarkan guru dan waktu
    $(`select[data-hari="${hari}"]`).not(changedSelect).each(function() {
        const otherMapelId = $(this).val();
        if (otherMapelId &&
            !otherMapelId.includes('istirahat') && !otherMapelId.includes('upacara') &&
            !otherMapelId.includes('kebaktian') && !otherMapelId.includes('ekstra') &&
            !otherMapelId.includes('pramuka')) {

            const otherWaktuMulai = $(this).data('waktu-mulai');
            const otherWaktuSelesai = $(this).data('waktu-selesai');

            // Cek apakah guru yang sama dan waktu bentrok
            const mataPelajaran = @json($mataPelajaran);
            const selectedMapel = mataPelajaran.find(m => m.id == selectedMapelId);
            const otherMapel = mataPelajaran.find(m => m.id == otherMapelId);

            const selectedGuruId = selectedMapel && selectedMapel.pengajar ? selectedMapel.pengajar.id : null;
            const otherGuruId = otherMapel && otherMapel.pengajar ? otherMapel.pengajar.id : null;

            if (selectedGuruId && otherGuruId && selectedGuruId === otherGuruId &&
                isTimeOverlap(waktuMulai, waktuSelesai, otherWaktuMulai, otherWaktuSelesai)) {
                // Cari informasi mata pelajaran dan guru untuk kedua jadwal
                const namaMapel = selectedMapel ? selectedMapel.nama_mapel : 'Mata Pelajaran';
                const namaGuru = selectedMapel && selectedMapel.pengajar ? selectedMapel.pengajar.name : 'Guru tidak diketahui';
                const namaMapelLain = otherMapel ? otherMapel.nama_mapel : 'Mata Pelajaran';

                // Dapatkan nama kelas saat ini
                const namaKelasSaatIni = $('input[name="nama_kelas_text"]').val() || 'Kelas ini';

                Swal.fire({
                    title: '⚠️ BENTROK JADWAL TERDETEKSI!',
                    html: `
                        <div style="text-align: left; margin: 20px 0;">
                            <p><strong>�‍🏫 Guru:</strong> ${namaGuru}</p>
                            <p><strong>� Hari:</strong> ${hari}</p>
                            <p><strong>🏫 Kelas:</strong> ${namaKelasSaatIni}</p>
                            <hr style="margin: 15px 0;">
                            <p><strong>⏰ Jadwal yang Bentrok:</strong></p>
                            <ul style="margin-left: 20px;">
                                <li><strong>${namaMapel}</strong>: ${waktuMulai} - ${waktuSelesai}</li>
                                <li><strong>${namaMapelLain}</strong>: ${otherWaktuMulai} - ${otherWaktuSelesai}</li>
                            </ul>
                            <hr style="margin: 15px 0;">
                            <p style="color: #dc3545; font-weight: bold;">
                                ❌ KONFLIK: Guru ${namaGuru} tidak dapat mengajar di dua tempat pada waktu yang bersamaan!
                            </p>
                            <p style="color: #6c757d; font-size: 0.9em;">
                                💡 <strong>Solusi:</strong> Pilih guru lain atau ubah salah satu waktu jadwal.
                            </p>
                        </div>
                    `,
                    icon: 'warning',
                    confirmButtonText: 'Saya Mengerti',
                    confirmButtonColor: '#dc3545',
                    width: '650px'
                });
                bentrok = true;
                return false; // break the each loop
            }
        }
    });
    
    return bentrok;
}

function isTimeOverlap(start1, end1, start2, end2) {
    return (start1 < end2 && end1 > start2);
}

// Fungsi untuk mengecek bentrok dengan jadwal yang sudah ada di database
function checkBentrokWithDatabase(mapelId, hari, waktuMulai, waktuSelesai, namaMapel, namaGuru) {
    const tahunAjaran = $('input[name="tahun_ajaran"]').val();
    const jadwalId = $('input[name="jadwal_id"]').val();
    const namaKelasSaatIni = $('input[name="nama_kelas_text"]').val() || 'Kelas ini';

    $.ajax({
        url: '{{ route("api.jadwal.guru") }}',
        type: 'GET',
        data: {
            mapel_id: mapelId,
            hari: hari,
            tahun_ajaran: tahunAjaran,
            jadwal_id: jadwalId
        },
        success: function(response) {
            if (response.jadwal && response.jadwal.length > 0) {
                // Cari jadwal yang bentrok berdasarkan waktu (guru yang sama)
                const jadwalBentrok = response.jadwal.find(j => {
                    return isTimeOverlap(waktuMulai, waktuSelesai, j.waktu_mulai, j.waktu_selesai);
                });

                if (jadwalBentrok) {
                    Swal.fire({
                        title: '⚠️ BENTROK JADWAL TERDETEKSI!',
                        html: `
                            <div style="text-align: left; margin: 20px 0;">
                                <p><strong>�‍🏫 Guru:</strong> ${namaGuru}</p>
                                <p><strong>� Hari:</strong> ${hari}</p>
                                <p><strong>⏰ Jam Bentrok:</strong> ${jadwalBentrok.waktu_mulai} - ${jadwalBentrok.waktu_selesai}</p>
                                <hr style="margin: 15px 0;">
                                <p><strong>🏫 Kelas yang sedang diedit:</strong> ${namaKelasSaatIni}</p>
                                <p><strong>📚 Mata Pelajaran yang sedang diedit:</strong> ${namaMapel}</p>
                                <hr style="margin: 15px 0;">
                                <p><strong>🏫 Kelas yang sudah diajar:</strong> ${jadwalBentrok.kelas}</p>
                                <p><strong>📚 Mata Pelajaran yang sudah diajar:</strong> ${jadwalBentrok.mata_pelajaran}</p>
                                <hr style="margin: 15px 0;">
                                <p style="color: #dc3545; font-weight: bold;">
                                    ❌ KONFLIK: Guru ${namaGuru} tidak dapat mengajar di dua tempat pada waktu yang bersamaan!
                                </p>
                                <p style="color: #6c757d; font-size: 0.9em;">
                                    Guru sudah memiliki jadwal mengajar <strong>${jadwalBentrok.mata_pelajaran}</strong> di kelas <strong>${jadwalBentrok.kelas}</strong> pada jam <strong>${jadwalBentrok.waktu_mulai}-${jadwalBentrok.waktu_selesai}</strong>.
                                </p>
                                <p style="color: #6c757d; font-size: 0.9em;">
                                    💡 <strong>Solusi:</strong> Pilih guru lain atau ubah waktu jadwal.
                                </p>
                            </div>
                        `,
                        icon: 'warning',
                        confirmButtonText: 'Saya Mengerti',
                        confirmButtonColor: '#dc3545',
                        width: '700px'
                    });
                }
            }
        },
        error: function(xhr, status, error) {
            console.error('Error checking bentrok with database:', error);
        }
    });
}

// Variabel global untuk menyimpan elemen yang sedang dipilih
let currentMapelElement = null;

// Fungsi untuk membuka modal pemilihan mata pelajaran
function openMapelModal(element) {
    currentMapelElement = element;
    $('#mapelModal').modal('show');
}

// Fungsi untuk memilih mata pelajaran
function selectMapel(option) {
    const value = option.getAttribute('data-value');
    const mapel = option.getAttribute('data-mapel');
    const guru = option.getAttribute('data-guru');

    if (currentMapelElement) {
        const hiddenInput = currentMapelElement.parentElement.querySelector('input[type="hidden"][name*="[mapel_id]"]');

        if (value === '') {
            // Kosong
            currentMapelElement.innerHTML = '<span>Klik untuk pilih</span>';
            currentMapelElement.className = 'mapel-display empty';
            hiddenInput.value = '';
        } else if (guru) {
            // Mata pelajaran dengan guru
            currentMapelElement.innerHTML = `
                <div class="mapel-name">${mapel}</div>
                <div class="guru-name">${guru}</div>
            `;
            currentMapelElement.className = 'mapel-display';
            hiddenInput.value = value;
        } else {
            // Kegiatan khusus
            currentMapelElement.innerHTML = `
                <div class="mapel-name">${mapel}</div>
            `;
            currentMapelElement.className = 'mapel-display kegiatan-khusus';
            hiddenInput.value = value;
        }

        // Cek bentrok setelah pemilihan
        checkBentrokByElement(currentMapelElement);
    }

    $('#mapelModal').modal('hide');
}

// Fungsi untuk setup event handlers
function setupMapelDisplayHandlers() {
    // Search functionality
    $('#searchMapel').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        $('.mapel-item, .kegiatan-item').each(function() {
            const searchData = $(this).data('search');
            if (searchData.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // Clear search when modal opens
    $('#mapelModal').on('shown.bs.modal', function() {
        $('#searchMapel').val('').trigger('input');
    });
}

// Fungsi untuk cek bentrok berdasarkan element
function checkBentrokByElement(element) {
    const hiddenInput = element.parentElement.querySelector('input[type="hidden"][name*="[mapel_id]"]');
    const selectedMapelId = hiddenInput.value;

    if (!selectedMapelId || selectedMapelId.includes('istirahat') ||
        selectedMapelId.includes('upacara') || selectedMapelId.includes('kebaktian') ||
        selectedMapelId.includes('ekstra') || selectedMapelId.includes('pramuka')) {
        return false;
    }

    const hari = element.getAttribute('data-hari');
    const waktuMulai = element.getAttribute('data-waktu-mulai');
    const waktuSelesai = element.getAttribute('data-waktu-selesai');
    let bentrok = false;

    // Cek bentrok dengan jadwal lain pada hari yang sama
    document.querySelectorAll(`[data-hari="${hari}"]`).forEach(function(otherElement) {
        if (otherElement !== element) {
            const otherHiddenInput = otherElement.parentElement.querySelector('input[type="hidden"][name*="[mapel_id]"]');
            const otherMapelId = otherHiddenInput ? otherHiddenInput.value : '';

            if (otherMapelId && otherMapelId === selectedMapelId &&
                !otherMapelId.includes('istirahat') && !otherMapelId.includes('upacara') &&
                !otherMapelId.includes('kebaktian') && !otherMapelId.includes('ekstra') &&
                !otherMapelId.includes('pramuka')) {

                const otherWaktuMulai = otherElement.getAttribute('data-waktu-mulai');
                const otherWaktuSelesai = otherElement.getAttribute('data-waktu-selesai');

                if (isTimeOverlap(waktuMulai, waktuSelesai, otherWaktuMulai, otherWaktuSelesai)) {
                    // Cari informasi mata pelajaran dan guru
                    const mataPelajaran = @json($mataPelajaran);
                    const selectedMapel = mataPelajaran.find(m => m.id == selectedMapelId);
                    const namaMapel = selectedMapel ? selectedMapel.nama_mapel : 'Mata Pelajaran';
                    const namaGuru = selectedMapel && selectedMapel.pengajar ? selectedMapel.pengajar.name : 'Guru tidak diketahui';

                    // Dapatkan nama kelas saat ini
                    const namaKelasSaatIni = $('input[name="nama_kelas_text"]').val() || 'Kelas ini';

                    Swal.fire({
                        title: '⚠️ BENTROK JADWAL TERDETEKSI!',
                        html: `
                            <div style="text-align: left; margin: 20px 0;">
                                <p><strong>📚 Mata Pelajaran:</strong> ${namaMapel}</p>
                                <p><strong>👨‍🏫 Guru:</strong> ${namaGuru}</p>
                                <p><strong>🏫 Kelas yang sedang diedit:</strong> ${namaKelasSaatIni}</p>
                                <p><strong>📅 Hari:</strong> ${hari}</p>
                                <p><strong>⏰ Jam Bentrok:</strong></p>
                                <ul style="margin-left: 20px;">
                                    <li>Jadwal 1: ${waktuMulai} - ${waktuSelesai}</li>
                                    <li>Jadwal 2: ${otherWaktuMulai} - ${otherWaktuSelesai}</li>
                                </ul>
                                <p style="color: #dc3545; font-weight: bold;">
                                    ❌ KONFLIK: Guru ${namaGuru} tidak dapat mengajar mata pelajaran ${namaMapel} di kelas ${namaKelasSaatIni} pada dua waktu yang berbeda dalam hari yang sama!
                                </p>
                                <p style="color: #6c757d; font-size: 0.9em;">
                                    💡 Solusi: Pilih guru lain atau ubah salah satu waktu jadwal.
                                </p>
                            </div>
                        `,
                        icon: 'warning',
                        confirmButtonText: 'Saya Mengerti',
                        confirmButtonColor: '#dc3545',
                        width: '600px'
                    });
                    bentrok = true;
                }
            }
        }
    });

    return bentrok;
}
</script>
@endsection


