<?php $__env->startSection('title', '<PERSON><PERSON>wal Per Guru'); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h3 class="card-title">
                <i class="fas fa-user-tie"></i> J<PERSON>wal Per Guru
            </h3>
            <div class="card-tools">
                <div class="btn-group">
                    <button type="button" class="btn btn-secondary btn-sm dropdown-toggle" data-toggle="dropdown">
                        <i class="fas fa-filter"></i> Tahun Ajaran: <?php echo e($tahunAjaranTerpilih); ?>

                    </button>
                    <div class="dropdown-menu">
                        <?php $__currentLoopData = $tahunAjaranList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ta): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a class="dropdown-item <?php echo e($ta == $tahunAjaranTerpilih ? 'active' : ''); ?>" 
                               href="<?php echo e(route('jadwal.perguru', ['tahun_ajaran' => $ta, 'guru_id' => $guruTerpilih])); ?>">
                                <?php echo e($ta); ?>

                            </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <!-- Filter Guru -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="guru_select">
                        <i class="fas fa-user-tie"></i> Pilih Guru:
                    </label>
                    <select id="guru_select" class="form-control" onchange="changeGuru()">
                        <option value="">-- Pilih Guru --</option>
                        <?php $__currentLoopData = $guruList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $guru): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($guru->id); ?>" <?php echo e($guru->id == $guruTerpilih ? 'selected' : ''); ?>>
                                <?php echo e($guru->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
            </div>
        </div>

        <?php if($guruData && $jadwalGuru->isNotEmpty()): ?>
            <!-- Info Guru -->
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle"></i> Informasi Guru</h5>
                <p class="mb-1"><strong>Nama:</strong> <?php echo e($guruData->name); ?></p>
                <p class="mb-1"><strong>Email:</strong> <?php echo e($guruData->email); ?></p>
                <p class="mb-0"><strong>Total Jam Mengajar:</strong> <?php echo e($jadwalGuru->count()); ?> jam pelajaran</p>
            </div>

            <!-- Tabel Jadwal -->
            <div class="table-responsive">
                <table class="table table-bordered jadwal-table">
                    <thead class="thead-dark">
                        <tr>
                            <th class="jam-column">
                                <i class="fas fa-clock"></i> Jam
                            </th>
                            <?php $__currentLoopData = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $hari): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <th class="hari-column text-center">
                                    <?php echo e($hari); ?>

                                </th>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                            // Buat array untuk menyimpan jadwal per hari dan jam
                            $jadwalData = [];
                            foreach($jadwalGuru as $jadwal) {
                                $jamKey = substr($jadwal->waktu_mulai, 0, 5) . '-' . substr($jadwal->waktu_selesai, 0, 5);
                                $jadwalData[$jadwal->hari][$jamKey] = $jadwal;
                            }

                            // Buat array jam unik dan urutkan
                            $jamSlots = [];
                            foreach($jadwalGuru as $jadwal) {
                                $jamKey = substr($jadwal->waktu_mulai, 0, 5) . '-' . substr($jadwal->waktu_selesai, 0, 5);
                                if (!in_array($jamKey, $jamSlots)) {
                                    $jamSlots[] = $jamKey;
                                }
                            }
                            
                            // Urutkan jam berdasarkan waktu mulai
                            usort($jamSlots, function($a, $b) {
                                $timeA = explode('-', $a)[0];
                                $timeB = explode('-', $b)[0];
                                return strcmp($timeA, $timeB);
                            });
                        ?>

                        <?php $__currentLoopData = $jamSlots; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $jam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td class="jam-cell font-weight-bold">
                                    <?php echo e($jam); ?>

                                </td>
                                <?php $__currentLoopData = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $hari): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <td class="mapel-cell">
                                        <?php if(isset($jadwalData[$hari][$jam])): ?>
                                            <?php $detail = $jadwalData[$hari][$jam]; ?>
                                            <div class="jadwal-item mapel">
                                                <div class="mapel-name">
                                                    <i class="fas fa-book"></i> <?php echo e($detail->mataPelajaran->nama_mapel); ?>

                                                </div>
                                                <div class="kelas-name">
                                                    <i class="fas fa-users"></i> <?php echo e($detail->jadwalPelajaran->nama_kelas_text); ?>

                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <div class="jadwal-item empty">
                                                <span class="text-muted">-</span>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>

            <!-- Ringkasan Jadwal -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-bar"></i> Ringkasan Jadwal</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php $__currentLoopData = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $hari): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $jadwalHari = $jadwalGuru->where('hari', $hari);
                                        $jumlahJam = $jadwalHari->count();
                                    ?>
                                    <div class="col-md-2">
                                        <div class="info-box">
                                            <span class="info-box-icon bg-info">
                                                <i class="fas fa-calendar-day"></i>
                                            </span>
                                            <div class="info-box-content">
                                                <span class="info-box-text"><?php echo e($hari); ?></span>
                                                <span class="info-box-number"><?php echo e($jumlahJam); ?> jam</span>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <?php elseif($guruData && $jadwalGuru->isEmpty()): ?>
            <div class="alert alert-warning">
                <h5><i class="fas fa-exclamation-triangle"></i> Tidak Ada Jadwal</h5>
                <p class="mb-0">Guru <strong><?php echo e($guruData->name); ?></strong> tidak memiliki jadwal mengajar pada tahun ajaran <?php echo e($tahunAjaranTerpilih); ?>.</p>
            </div>
        <?php else: ?>
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle"></i> Pilih Guru</h5>
                <p class="mb-0">Silakan pilih guru dari dropdown di atas untuk melihat jadwal mengajar.</p>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('css'); ?>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap4-theme@1.0.0/dist/select2-bootstrap4.min.css" rel="stylesheet" />
<style>
    /* Styling untuk tabel jadwal */
    .jadwal-table {
        margin-top: 20px;
        font-size: 0.9rem;
    }

    .jadwal-table thead th {
        background-color: #343a40;
        color: white;
        text-align: center;
        font-weight: 600;
        border-color: #495057;
        padding: 12px 8px;
        vertical-align: middle;
    }

    .jadwal-table tbody td {
        vertical-align: middle;
        border-color: #dee2e6;
        padding: 8px;
    }

    /* Kolom jam (kolom pertama) */
    .jadwal-table .jam-cell {
        background-color: #e3f2fd;
        font-weight: 600;
        text-align: center;
        width: 100px;
        min-width: 100px;
        max-width: 100px;
        white-space: nowrap;
        font-size: 0.85rem;
    }

    /* Kolom mata pelajaran */
    .jadwal-table .mapel-cell {
        background-color: #ffffff;
        min-width: 180px;
        position: relative;
        padding: 4px;
    }

    /* Styling untuk jadwal item */
    .jadwal-item {
        padding: 8px;
        border-radius: 4px;
        min-height: 45px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .jadwal-item.mapel {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
    }

    .jadwal-item.empty {
        background-color: #ffffff;
        border: 2px dashed #dee2e6;
        color: #6c757d;
        font-style: italic;
        align-items: center;
        justify-content: center;
        min-height: 40px;
    }

    .mapel-name {
        font-weight: 600;
        font-size: 0.85rem;
        color: #495057;
        line-height: 1.2;
        margin-bottom: 2px;
    }

    .kelas-name {
        font-size: 0.75rem;
        color: #6c757d;
        line-height: 1.1;
    }

    /* Zebra striping untuk baris */
    .jadwal-table tbody tr:nth-child(even) .mapel-cell {
        background-color: #f8f9fa;
    }

    .jadwal-table tbody tr:nth-child(odd) .mapel-cell {
        background-color: #ffffff;
    }

    /* Icon styling */
    .card-header i, .mapel-name i, .kelas-name i {
        margin-right: 5px;
        color: #6c757d;
    }

    /* Info box styling */
    .info-box {
        display: block;
        min-height: 70px;
        background: #fff;
        width: 100%;
        box-shadow: 0 1px 1px rgba(0,0,0,0.1);
        border-radius: 2px;
        margin-bottom: 15px;
    }

    .info-box-icon {
        border-top-left-radius: 2px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 2px;
        display: block;
        float: left;
        height: 70px;
        width: 70px;
        text-align: center;
        font-size: 45px;
        line-height: 70px;
        background: rgba(0,0,0,0.2);
    }

    .info-box-content {
        padding: 5px 10px;
        margin-left: 70px;
    }

    .info-box-text {
        text-transform: uppercase;
        font-weight: bold;
        font-size: 12px;
    }

    .info-box-number {
        display: block;
        font-weight: bold;
        font-size: 18px;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .jadwal-table {
            font-size: 0.8rem;
        }

        .jadwal-table .jam-cell {
            width: 80px;
            min-width: 80px;
            max-width: 80px;
            font-size: 0.75rem;
        }

        .jadwal-table .mapel-cell {
            min-width: 120px;
        }
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() {
    // Initialize Select2 untuk dropdown guru
    $('#guru_select').select2({
        theme: 'bootstrap4',
        placeholder: "Pilih Guru",
        allowClear: true,
        width: '100%'
    });
});

function changeGuru() {
    const guruId = document.getElementById('guru_select').value;
    const tahunAjaran = '<?php echo e($tahunAjaranTerpilih); ?>';
    
    if (guruId) {
        window.location.href = `<?php echo e(route('jadwal.perguru')); ?>?guru_id=${guruId}&tahun_ajaran=${tahunAjaran}`;
    } else {
        window.location.href = `<?php echo e(route('jadwal.perguru')); ?>?tahun_ajaran=${tahunAjaran}`;
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\simaspeloporh2_Jadwal Ok2\resources\views/jadwal/gujad.blade.php ENDPATH**/ ?>