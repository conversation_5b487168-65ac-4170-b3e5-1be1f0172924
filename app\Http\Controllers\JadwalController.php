<?php

namespace App\Http\Controllers;

use App\Models\JadwalPelajaran;
use App\Models\DetailJadwal;
use App\Models\Kelas;
use App\Models\MataPelajaran;
use App\Models\User;
use Illuminate\Http\Request;
use Carbon\Carbon;
use DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\JadwalExport;
use App\Exports\JadwalExportPerKelas;
use App\Exports\JadwalExportPerGuru;
use App\Models\TahunAjaran;
 
class JadwalController extends Controller
{
    public function index(Request $request)
    {
        $user = auth()->user();
        
        // Ambil data tahun ajaran dari database
        $tahunAjaranList = TahunAjaran::orderBy('nama', 'desc')->pluck('nama')->toArray();
        $tahunAjaranAktif = TahunAjaran::where('aktif', true)->first()->nama ?? $this->getTahunAjaranFallback()[0];
        $tahunAjaranTerpilih = $request->get('tahun_ajaran', $tahunAjaranAktif);

        $query = JadwalPelajaran::where('tahun_ajaran', $tahunAjaranTerpilih)
            ->with(['kelas', 'detailJadwal' => function($query) {
                $query->orderBy('hari')->orderBy('waktu_mulai');
            }, 'detailJadwal.mataPelajaran']);

        // Jika route adalah jadwal.mengajar, filter hanya untuk guru yang login
        if ($request->route()->getName() === 'jadwal.mengajar') {
            $query->whereHas('detailJadwal.mataPelajaran', function($query) use ($user) {
                $query->where('pengajar_id', $user->id);
            });
        }

        $jadwalList = $query->get();

        return view('jadwal.index', compact('jadwalList', 'tahunAjaranList', 'tahunAjaranTerpilih'));
    }

    public function create()
    {
        // Ambil data untuk dropdown
        $kelas = Kelas::all();
        $mataPelajaran = MataPelajaran::with('pengajar')->get();
        
        // Definisikan special events
        $specialEvents = [
            ['id' => 'istirahat', 'nama' => 'Istirahat'],
            ['id' => 'upacara', 'nama' => 'Upacara'],
            ['id' => 'kebaktian', 'nama' => 'Kebaktian'],
            ['id' => 'ekstra', 'nama' => 'Ekstra'],
            ['id' => 'pramuka', 'nama' => 'Pramuka']
        ];
        
        // Definisikan layouts
        $layouts = [
            'layout_tk' => 'Layout TK',
            'layout_sd' => 'Layout SD',
            'layout_smp' => 'Layout SMP',
            'layout_sma' => 'Layout SMA'
        ];
        
        // Ambil daftar tahun ajaran
        $tahunAjaranList = TahunAjaran::pluck('nama')->toArray();
        
        return view('jadwal.create', compact('kelas', 'mataPelajaran', 'specialEvents', 'layouts', 'tahunAjaranList'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'kelas_id' => 'required',
            'nama_kelas_text' => 'required',
            'wali_kelas' => 'required',
            'tahun_ajaran' => 'required',
            'layout_type' => 'required',
            'jadwal' => 'required|array'
        ]);

        try {
            DB::beginTransaction();

            // Validasi minimal 1 mapel per hari
            $jadwalPerHari = [];
            foreach ($request->jadwal as $key => $detail) {
                if (!empty($detail['mapel_id']) && !str_contains($detail['mapel_id'], 'istirahat')) {
                    $hari = $detail['hari'];
                    $jadwalPerHari[$hari] = ($jadwalPerHari[$hari] ?? 0) + 1;
                }
            }

            // Cek setiap hari memiliki minimal 1 mapel
            $hari = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat'];
            foreach ($hari as $h) {
                if (!isset($jadwalPerHari[$h]) || $jadwalPerHari[$h] < 1) {
                    DB::rollback();
                    return back()->with('error', "Hari {$h} harus memiliki minimal 1 mata pelajaran")
                                ->withInput(); // Tambahkan withInput() agar data form tidak hilang
                }
            }

            // Cek bentrok jadwal guru (kecuali untuk istirahat)
            $bentrok = $this->cekBentrokJadwal($request->jadwal, $request->tahun_ajaran);
            if ($bentrok) {
                DB::rollback();
                return back()->with('error', 'Terdapat bentrok jadwal: ' . $bentrok)
                            ->withInput(); // Tambahkan withInput() agar data form tidak hilang
            }

            $jadwal = JadwalPelajaran::create([
                'kelas_id' => $request->kelas_id,
                'nama_kelas_text' => $request->nama_kelas_text,
                'wali_kelas' => $request->wali_kelas,
                'tahun_ajaran' => $request->tahun_ajaran,
                'layout_type' => $request->layout_type
            ]);

            // Simpan detail jadwal dengan validasi tambahan
            foreach ($request->jadwal as $detail) {
                if (!empty($detail['mapel_id'])) {
                    $isSpecialEvent = str_contains($detail['mapel_id'], 'istirahat') ||
                                     str_contains($detail['mapel_id'], 'upacara') ||
                                     str_contains($detail['mapel_id'], 'kebaktian') ||
                                     str_contains($detail['mapel_id'], 'ekstra') ||
                                     str_contains($detail['mapel_id'], 'pramuka');
                    
                    // Validasi waktu
                    if (!isset($detail['waktu_mulai']) || !isset($detail['waktu_selesai'])) {
                        throw new \Exception('Waktu mulai dan selesai harus diisi');
                    }

                    // Validasi format waktu
                    if (!preg_match('/^([0-1][0-9]|2[0-3]):[0-5][0-9]$/', $detail['waktu_mulai']) ||
                        !preg_match('/^([0-1][0-9]|2[0-3]):[0-5][0-9]$/', $detail['waktu_selesai'])) {
                        throw new \Exception('Format waktu tidak valid');
                    }

                    DetailJadwal::create([
                        'jadwal_pelajaran_id' => $jadwal->id,
                        'mata_pelajaran_id' => $isSpecialEvent ? null : $detail['mapel_id'],
                        'hari' => $detail['hari'],
                        'waktu_mulai' => $detail['waktu_mulai'],
                        'waktu_selesai' => $detail['waktu_selesai'],
                        'is_istirahat' => str_contains($detail['mapel_id'], 'istirahat'),
                        'keterangan' => $isSpecialEvent ? ucfirst($detail['mapel_id']) : null
                    ]);
                }
            }

            DB::commit();
            return redirect()->route('jadwal.index')->with('success', 'Jadwal berhasil dibuat');
        } catch (\Exception $e) {
            DB::rollback();
            \Log::error('Error creating jadwal: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());
            return back()->with('error', 'Terjadi kesalahan saat menyimpan jadwal: ' . $e->getMessage())
                        ->withInput(); // Tambahkan withInput() agar data form tidak hilang
        }
    }

    public function edit($id)
    {
        // Muat jadwal dengan relasi yang diperlukan
        $jadwal = JadwalPelajaran::with(['kelas', 'detailJadwal.mataPelajaran'])->findOrFail($id);
        
        // Pastikan detailJadwal diurutkan berdasarkan hari dan waktu mulai
        $jadwal->detailJadwal = $jadwal->detailJadwal->sortBy([
            ['hari', 'asc'],
            ['waktu_mulai', 'asc']
        ])->values();
        
        // Ambil data untuk dropdown
        $kelas = Kelas::all();
        $mataPelajaran = MataPelajaran::with('pengajar')->get();
        
        // Definisikan special events
        $specialEvents = [
            ['id' => 'istirahat', 'nama' => 'Istirahat'],
            ['id' => 'upacara', 'nama' => 'Upacara'],
            ['id' => 'kebaktian', 'nama' => 'Kebaktian'],
            ['id' => 'ekstra', 'nama' => 'Ekstra'],
            ['id' => 'pramuka', 'nama' => 'Pramuka']
        ];
        
        // Definisikan layouts
        $layouts = [
            'layout_tk' => 'Layout TK',
            'layout_sd' => 'Layout SD',
            'layout_smp' => 'Layout SMP',
            'layout_sma' => 'Layout SMA'
        ];
        
        // Ambil daftar tahun ajaran
        $tahunAjaranList = TahunAjaran::pluck('nama')->toArray();
        
        return view('jadwal.edit', compact('jadwal', 'kelas', 'mataPelajaran', 'specialEvents', 'layouts', 'tahunAjaranList'));
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'kelas_id' => 'required',
            'nama_kelas_text' => 'required',
            'wali_kelas' => 'required',
            'tahun_ajaran' => 'required',
            'layout_type' => 'required',
            'jadwal' => 'required|array'
        ]);

        try {
            DB::beginTransaction();
            
            $jadwal = JadwalPelajaran::findOrFail($id);

            // Validasi minimal 1 mapel per hari
            $jadwalPerHari = [];
            foreach ($request->jadwal as $key => $detail) {
                if (!empty($detail['mapel_id']) && !str_contains($detail['mapel_id'], 'istirahat')) {
                    $hari = $detail['hari'];
                    $jadwalPerHari[$hari] = ($jadwalPerHari[$hari] ?? 0) + 1;
                }
            }

            // Cek setiap hari memiliki minimal 1 mapel
            $hari = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat'];
            foreach ($hari as $h) {
                if (!isset($jadwalPerHari[$h]) || $jadwalPerHari[$h] < 1) {
                    DB::rollback();
                    return back()->with('error', "Hari {$h} harus memiliki minimal 1 mata pelajaran")
                                ->withInput(); // Tambahkan withInput() agar data form tidak hilang
                }
            }

            // Cek bentrok jadwal guru (kecuali untuk istirahat)
            $bentrok = $this->cekBentrokJadwal($request->jadwal, $request->tahun_ajaran, $id);
            if ($bentrok) {
                DB::rollback();
                return back()->with('error', 'Terdapat bentrok jadwal: ' . $bentrok)
                            ->withInput(); // Tambahkan withInput() agar data form tidak hilang
            }

            // Update data jadwal utama
            $jadwal->update([
                'kelas_id' => $request->kelas_id,
                'nama_kelas_text' => $request->nama_kelas_text,
                'wali_kelas' => $request->wali_kelas,
                'tahun_ajaran' => $request->tahun_ajaran,
                'layout_type' => $request->layout_type
            ]);

            // Hapus detail jadwal yang lama
            $jadwal->detailJadwal()->delete();

            // Simpan detail jadwal yang baru
            foreach ($request->jadwal as $detail) {
                if (!empty($detail['mapel_id'])) {
                    $isSpecialEvent = str_contains($detail['mapel_id'], 'istirahat') ||
                                     str_contains($detail['mapel_id'], 'upacara') ||
                                     str_contains($detail['mapel_id'], 'kebaktian') ||
                                     str_contains($detail['mapel_id'], 'ekstra') ||
                                     str_contains($detail['mapel_id'], 'pramuka');
                    
                    // Validasi waktu
                    if (!isset($detail['waktu_mulai']) || !isset($detail['waktu_selesai'])) {
                        throw new \Exception('Waktu mulai dan selesai harus diisi');
                    }

                    // Validasi format waktu
                    if (!preg_match('/^([0-1][0-9]|2[0-3]):[0-5][0-9]$/', $detail['waktu_mulai']) ||
                        !preg_match('/^([0-1][0-9]|2[0-3]):[0-5][0-9]$/', $detail['waktu_selesai'])) {
                        throw new \Exception('Format waktu tidak valid');
                    }

                    DetailJadwal::create([
                        'jadwal_pelajaran_id' => $jadwal->id,
                        'mata_pelajaran_id' => $isSpecialEvent ? null : $detail['mapel_id'],
                        'hari' => $detail['hari'],
                        'waktu_mulai' => $detail['waktu_mulai'],
                        'waktu_selesai' => $detail['waktu_selesai'],
                        'is_istirahat' => str_contains($detail['mapel_id'], 'istirahat'),
                        'keterangan' => $isSpecialEvent ? ucfirst($detail['mapel_id']) : null
                    ]);
                }
            }

            DB::commit();
            return redirect()->route('jadwal.index')->with('success', 'Jadwal berhasil diperbarui');
        } catch (\Exception $e) {
            DB::rollback();
            \Log::error('Error updating jadwal: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());
            return back()->with('error', 'Terjadi kesalahan saat memperbarui jadwal: ' . $e->getMessage())
                        ->withInput(); // Tambahkan withInput() agar data form tidak hilang
        }
    }

    public function destroy($id)
    {
        try {
            DB::beginTransaction();
            
            $jadwal = JadwalPelajaran::findOrFail($id);
            $jadwal->detailJadwal()->delete();
            $jadwal->delete();
             
            DB::commit();
            return redirect()->route('jadwal.index')->with('success', 'Jadwal berhasil dihapus');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Terjadi kesalahan saat menghapus jadwal');
        }
    }

    private function cekBentrokJadwal($jadwalBaru, $tahunAjaran, $excludeJadwalId = null)
    {
        $existingJadwal = DetailJadwal::with(['jadwalPelajaran', 'mataPelajaran.pengajar'])
            ->whereHas('jadwalPelajaran', function($q) use ($tahunAjaran, $excludeJadwalId) {
                $q->where('tahun_ajaran', $tahunAjaran);
                if ($excludeJadwalId) {
                    $q->where('id', '!=', $excludeJadwalId);
                }
            })->get();

        foreach ($jadwalBaru as $jadwal) {
            // Skip pengecekan untuk istirahat, upacara, kebaktian, ekstra, dan pramuka
            if (empty($jadwal['mapel_id']) ||
                str_contains($jadwal['mapel_id'], 'istirahat') ||
                str_contains($jadwal['mapel_id'], 'upacara') ||
                str_contains($jadwal['mapel_id'], 'kebaktian') ||
                str_contains($jadwal['mapel_id'], 'ekstra') ||
                str_contains($jadwal['mapel_id'], 'pramuka')) {
                continue;
            }

            foreach ($existingJadwal as $existing) {
                if ($jadwal['hari'] == $existing->hari &&
                    $this->isTimeOverlap(
                        $jadwal['waktu_mulai'],
                        $jadwal['waktu_selesai'],
                        $existing->waktu_mulai,
                        $existing->waktu_selesai
                    ) &&
                    $jadwal['mapel_id'] == $existing->mata_pelajaran_id
                ) {
                    // Informasi detail untuk pesan error yang lebih lengkap
                    $namaGuru = $existing->mataPelajaran->pengajar->name ?? 'Guru tidak diketahui';
                    $namaMapel = $existing->mataPelajaran->nama_mapel ?? 'Mata pelajaran tidak diketahui';
                    $kelasYangSudahAda = $existing->jadwalPelajaran->nama_kelas_text ?? 'Kelas tidak diketahui';
                    $waktuMulai = substr($existing->waktu_mulai, 0, 5); // Format HH:MM
                    $waktuSelesai = substr($existing->waktu_selesai, 0, 5); // Format HH:MM

                    return "BENTROK JADWAL TERDETEKSI!\n\n" .
                           "📚 Mata Pelajaran: {$namaMapel}\n" .
                           "👨‍🏫 Guru: {$namaGuru}\n" .
                           "🏫 Kelas yang sudah diajar: {$kelasYangSudahAda}\n" .
                           "📅 Hari: {$existing->hari}\n" .
                           "⏰ Jam: {$waktuMulai} - {$waktuSelesai}\n\n" .
                           "❌ KONFLIK: Guru {$namaGuru} sudah memiliki jadwal mengajar mata pelajaran {$namaMapel} di kelas {$kelasYangSudahAda} pada hari {$existing->hari} jam {$waktuMulai}-{$waktuSelesai}.\n\n" .
                           "Silakan pilih guru lain atau ubah waktu jadwal.";
                }
            }
        }
        return false;
    }

    public function getJadwalByGuru(Request $request)
    {
        $mapelId = $request->mapel_id;
        $hari = $request->hari;
        $tahunAjaran = $request->tahun_ajaran;
        $jadwalId = $request->jadwal_id; // ID jadwal yang sedang diedit (jika ada)

        // Cari mata pelajaran
        $mataPelajaran = MataPelajaran::find($mapelId);
        if (!$mataPelajaran || !$mataPelajaran->pengajar) {
            return response()->json(['jadwal' => []]);
        }

        $guruId = $mataPelajaran->pengajar->id;

        // Ambil jadwal guru pada hari yang sama
        $query = DetailJadwal::with(['jadwalPelajaran', 'mataPelajaran'])
            ->whereHas('jadwalPelajaran', function($q) use ($tahunAjaran) {
                $q->where('tahun_ajaran', $tahunAjaran);
            })
            ->whereHas('mataPelajaran.pengajar', function($q) use ($guruId) {
                $q->where('id', $guruId);
            })
            ->where('hari', $hari);

        // Jika sedang edit, exclude jadwal yang sedang diedit
        if ($jadwalId) {
            $query->whereHas('jadwalPelajaran', function($q) use ($jadwalId) {
                $q->where('id', '!=', $jadwalId);
            });
        }

        $jadwalGuru = $query->get()->map(function($detail) {
            return [
                'mata_pelajaran' => $detail->mataPelajaran->nama_mapel ?? 'Mata Pelajaran',
                'guru' => $detail->mataPelajaran->pengajar->name ?? 'Guru tidak diketahui',
                'kelas' => $detail->jadwalPelajaran->nama_kelas_text ?? 'Kelas tidak diketahui',
                'hari' => $detail->hari,
                'waktu_mulai' => substr($detail->waktu_mulai, 0, 5),
                'waktu_selesai' => substr($detail->waktu_selesai, 0, 5)
            ];
        });

        return response()->json(['jadwal' => $jadwalGuru]);
    }

    private function isTimeOverlap($start1, $end1, $start2, $end2)
    {
        return ($start1 < $end2 && $end1 > $start2);
    }

    private function getTahunAjaranFallback()
    {
        $tahunSekarang = now()->year;
        $bulanSekarang = now()->month;
        $tahunMulai = $bulanSekarang >= 7 ? $tahunSekarang : $tahunSekarang - 1;
        return [$tahunMulai . '/' . ($tahunMulai + 1)];
    }

    public function export($tahunAjaran, Request $request)
    {
        try {
            $tahunAjaranDb = str_replace('-', '/', $tahunAjaran);
            $type = $request->query('type', 'all');
            
            $jadwalList = JadwalPelajaran::where('tahun_ajaran', $tahunAjaranDb)
                ->with([
                    'kelas',
                    'detailJadwal' => function($query) {
                        $query->orderBy('hari')->orderBy('waktu_mulai');
                    },
                    'detailJadwal.mataPelajaran',
                    'detailJadwal.mataPelajaran.pengajar'
                ])
                ->orderBy('nama_kelas_text')
                ->get();

            if ($jadwalList->isEmpty()) {
                return back()->with('error', 'Tidak ada data jadwal untuk tahun ajaran ini');
            }

            $fileName = 'Jadwal_Pelajaran_' . str_replace('/', '-', $tahunAjaranDb);
            
            switch ($type) {
                case 'kelas':
                    $fileName .= '_PerKelas.xlsx';
                    return Excel::download(new JadwalExportPerKelas($jadwalList), $fileName);
                
                case 'guru':
                    $fileName .= '_PerGuru.xlsx';
                    return Excel::download(new JadwalExportPerGuru($jadwalList), $fileName);
                
                default:
                    $fileName .= '_Lengkap.xlsx';
                    return Excel::download(new JadwalExport($jadwalList), $fileName);
            }
        } catch (\Exception $e) {
            \Log::error('Export error: ' . $e->getMessage());
            return back()->with('error', 'Terjadi kesalahan saat mengekspor jadwal');
        }
    }

    public function jadwalGuru(Request $request)
    {
        $user = auth()->user();
        
        // Ambil data tahun ajaran dari database
        $tahunAjaranList = TahunAjaran::orderBy('nama', 'desc')->pluck('nama')->toArray();
        $tahunAjaranAktif = TahunAjaran::where('aktif', true)->first()->nama ?? $this->getTahunAjaranFallback()[0];
        $tahunAjaranTerpilih = $request->get('tahun_ajaran', $tahunAjaranAktif);
        $guruId = auth()->id();

        // Ambil detail jadwal untuk guru yang login
        $jadwalGuru = DetailJadwal::with(['jadwalPelajaran', 'mataPelajaran'])
            ->whereHas('mataPelajaran', function($q) use ($guruId) {
                $q->whereHas('pengajar', function($q) use ($guruId) {
                    $q->where('id', $guruId);
                });
            })
            ->whereHas('jadwalPelajaran', function($q) use ($tahunAjaranTerpilih) {
                $q->where('tahun_ajaran', $tahunAjaranTerpilih);
            })
            ->get()
            ->sortBy(function($detail) {
                $hariUrutan = [
                    'Senin' => 1,
                    'Selasa' => 2,
                    'Rabu' => 3,
                    'Kamis' => 4,
                    'Jumat' => 5
                ];
                return $hariUrutan[$detail->hari];
            });

        return view('jadwal.guru', compact('jadwalGuru', 'tahunAjaranList', 'tahunAjaranTerpilih'));
    }

    public function jadwalMengajar(Request $request)
    {
        $user = auth()->user();
        
        // Ambil data tahun ajaran dari database
        $tahunAjaranList = TahunAjaran::orderBy('nama', 'desc')->pluck('nama')->toArray();
        $tahunAjaranAktif = TahunAjaran::where('aktif', true)->first()->nama ?? $this->getTahunAjaranFallback()[0];
        $tahunAjaranTerpilih = $request->get('tahun_ajaran', $tahunAjaranAktif);

        $jadwalList = JadwalPelajaran::where('tahun_ajaran', $tahunAjaranTerpilih)
            ->with(['kelas', 'detailJadwal' => function($query) {
                $query->orderBy('hari')->orderBy('waktu_mulai');
            }, 'detailJadwal.mataPelajaran'])
            ->whereHas('detailJadwal.mataPelajaran', function($query) use ($user) {
                $query->where('pengajar_id', $user->id);
            })
            ->get();

        return view('jadwal.mengajar', compact('jadwalList', 'tahunAjaranList', 'tahunAjaranTerpilih'));
    }

    public function show($id)
    {
        $jadwal = JadwalPelajaran::with(['kelas', 'detailJadwal.mataPelajaran.pengajar'])
            ->findOrFail($id);

        return view('jadwal.show', compact('jadwal'));
    }

    public function perguru(Request $request)
    {
        $user = auth()->user();

        // Ambil data tahun ajaran dari database
        $tahunAjaranList = TahunAjaran::orderBy('nama', 'desc')->pluck('nama')->toArray();
        $tahunAjaranAktif = TahunAjaran::where('aktif', true)->first()->nama ?? $this->getTahunAjaranFallback()[0];
        $tahunAjaranTerpilih = $request->get('tahun_ajaran', $tahunAjaranAktif);

        // Ambil semua guru yang mengajar (sederhana)
        $guruList = User::where('role', 'Guru')
            ->orderBy('name')
            ->get();

        // Ambil guru yang dipilih dari request, default ke guru pertama
        $guruTerpilih = $request->get('guru_id');
        if (!$guruTerpilih && $guruList->isNotEmpty()) {
            $guruTerpilih = $guruList->first()->id;
        }

        $jadwalGuru = collect();
        $guruData = null;

        if ($guruTerpilih) {
            // Ambil data guru yang dipilih
            $guruData = User::find($guruTerpilih);

            // Ambil detail jadwal untuk guru yang dipilih
            $jadwalGuru = DetailJadwal::with(['jadwalPelajaran', 'mataPelajaran'])
                ->whereHas('mataPelajaran', function($q) use ($guruTerpilih) {
                    $q->where('pengajar_id', $guruTerpilih);
                })
                ->whereHas('jadwalPelajaran', function($q) use ($tahunAjaranTerpilih) {
                    $q->where('tahun_ajaran', $tahunAjaranTerpilih);
                })
                ->get()
                ->sortBy(function($detail) {
                    $hariUrutan = [
                        'Senin' => 1,
                        'Selasa' => 2,
                        'Rabu' => 3,
                        'Kamis' => 4,
                        'Jumat' => 5
                    ];
                    return $hariUrutan[$detail->hari] * 1000 + strtotime($detail->waktu_mulai);
                });
        }

        return view('jadwal.gujad', compact('jadwalGuru', 'tahunAjaranList', 'tahunAjaranTerpilih', 'guruList', 'guruTerpilih', 'guruData'));
    }

    public function jadlaykot(Request $request)
    {
        $user = auth()->user();

        // Ambil data tahun ajaran dari database
        $tahunAjaranList = TahunAjaran::orderBy('nama', 'desc')->pluck('nama')->toArray();
        $tahunAjaranAktif = TahunAjaran::where('aktif', true)->first()->nama ?? $this->getTahunAjaranFallback()[0];
        $tahunAjaranTerpilih = $request->get('tahun_ajaran', $tahunAjaranAktif);

        $query = JadwalPelajaran::where('tahun_ajaran', $tahunAjaranTerpilih)
            ->with(['kelas', 'detailJadwal' => function($query) {
                $query->orderBy('hari')->orderBy('waktu_mulai');
            }, 'detailJadwal.mataPelajaran.pengajar']);

        // Jika user adalah guru, filter hanya untuk guru yang login
        if ($user->role === 'guru') {
            $query->whereHas('detailJadwal.mataPelajaran', function($query) use ($user) {
                $query->where('pengajar_id', $user->id);
            });
        }

        $jadwalList = $query->get();

        return view('jadwal.jadlaykot', compact('jadwalList', 'tahunAjaranList', 'tahunAjaranTerpilih'));
    }
}





















